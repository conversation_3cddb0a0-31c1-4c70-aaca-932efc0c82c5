import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { httpBatchLink } from '@trpc/client';
import { Toaster } from 'react-hot-toast';
import superjson from 'superjson';
import { api, trpcConfig } from './lib/trpc';
import { AuthProvider } from './contexts/AuthContext';
import Layout from './components/Layout';
import Dashboard from './pages/Dashboard';
import Services from './pages/Services';
import Database from './pages/Database';
import VectorDatabase from './pages/VectorDatabase';
import ConfigurationManager from './pages/ConfigurationManager';
import ModelManager from './pages/ModelManager';
import Users from './pages/Users';
import Logs from './pages/Logs';
import Settings from './pages/Settings';
import FileManager from './pages/FileManager';
import DatabaseQuery from './pages/DatabaseQuery';
import ApiTesting from './pages/ApiTesting';
import { EnvironmentManager } from './pages/EnvironmentManager';
import DockerManager from './pages/DockerManager';
import BackupRestore from './pages/BackupRestore';
import SecurityMonitoring from './pages/SecurityMonitoring';
import EtcdManager from './pages/EtcdManager';
import GrafanaIntegration from './pages/GrafanaIntegration';
import DataFlowDiagram from './pages/DataFlowDiagram';

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      retry: 1,
    },
  },
});

const trpcClient = api.createClient({
  links: [
    httpBatchLink({
      url: trpcConfig.url,
      headers: trpcConfig.headers,
      transformer: superjson,
    }),
  ],
});

function App() {
  return (
    <api.Provider client={trpcClient} queryClient={queryClient}>
      <QueryClientProvider client={queryClient}>
        <AuthProvider>
          <Router>
            <div className="min-h-screen bg-gray-50">
              <Layout>
                <Routes>
              <Route path="/" element={<Dashboard />} />
              <Route path="/services" element={<Services />} />
              <Route path="/database" element={<Database />} />
              <Route path="/database/query" element={<DatabaseQuery />} />
              <Route path="/vector-database" element={<VectorDatabase />} />
              <Route path="/configuration" element={<ConfigurationManager />} />
              <Route path="/etcd" element={<EtcdManager />} />
              <Route path="/models" element={<ModelManager />} />
              <Route path="/users" element={<Users />} />
              <Route path="/logs" element={<Logs />} />
              <Route path="/files" element={<FileManager />} />
              <Route path="/api-testing" element={<ApiTesting />} />
              <Route path="/docker" element={<DockerManager />} />
              <Route path="/environment" element={<EnvironmentManager />} />
              <Route path="/backup-restore" element={<BackupRestore />} />
              <Route path="/security" element={<SecurityMonitoring />} />
              <Route path="/grafana" element={<GrafanaIntegration />} />
              <Route path="/data-flow" element={<DataFlowDiagram />} />
              <Route path="/settings" element={<Settings />} />
                </Routes>
              </Layout>
              <Toaster position="top-right" />
            </div>
          </Router>
        </AuthProvider>
      </QueryClientProvider>
    </api.Provider>
  );
}

export default App;
