import { createTRPCReact } from '@trpc/react-query';
import { createTRPCClient, httpBatchLink } from '@trpc/client';
import superjson from 'superjson';
import type { AppRouter } from '../types/api';

const API_BASE = 'http://localhost:4001';

// Create tRPC React hooks
export const api = createTRPCReact<AppRouter>();

// Legacy export for backward compatibility
export const trpc = api;

// Create vanilla tRPC client for non-React usage
export const trpcClient = createTRPCClient<AppRouter>({
  links: [
    httpBatchLink({
      url: `${API_BASE}/trpc`,
      headers: {
        'Content-Type': 'application/json',
      },
      transformer: superjson,
    }),
  ],
});

// tRPC client configuration
export const trpcConfig = {
  url: `${API_BASE}/trpc`,
  headers: {
    'Content-Type': 'application/json',
  },
};

// Re-export types from generated types file
export type {
  VectorDocument,
  Collection,
  SearchResult,
  DatabaseTable,
  TableColumn,
  EtcdKeyValue,
  ServiceInstance,
  DockerContainer,
  SystemMetrics,
  ServiceHealth,
  SystemStats,
  AIModel,
  TrainingJob,
  ModelType,
  ModelStatus,
  TrainingStatus,
  User,
  UserRole,
  ApiResponse,
  Alert
} from '../types/api';
