import { create } from 'zustand';

interface WebSocketMessage {
  type: string;
  data: any;
  timestamp: string;
}

interface AlertMessage {
  id: string;
  type: 'info' | 'warning' | 'error' | 'success';
  title: string;
  message: string;
  timestamp: string;
  read: boolean;
}

interface SystemMetrics {
  cpu: {
    usage: number;
    temperature?: number;
    cores: number;
  };
  memory: {
    total: number;
    used: number;
    available: number;
    percentage: number;
  };
  disk: {
    total: number;
    used: number;
    available: number;
    percentage: number;
  };
  network: {
    bytesIn: number;
    bytesOut: number;
    packetsIn: number;
    packetsOut: number;
  };
  system: {
    uptime: number;
    platform: string;
    arch: string;
    hostname: string;
  };
  timestamp: string;
}

interface DockerContainer {
  id: string;
  name: string;
  image: string;
  status: 'running' | 'stopped' | 'error' | 'exited' | 'created' | 'restarting';
  state: string;
  ports: Array<{
    privatePort: number;
    publicPort?: number;
    type: string;
  }>;
  created: string;
  uptime?: string;
  stats?: {
    cpuUsage: number;
    memoryUsage: number;
    memoryLimit: number;
    networkRx: number;
    networkTx: number;
  };
}

interface DockerData {
  running: boolean;
  containers: DockerContainer[];
  error?: string;
}

interface WebSocketStore {
  connected: boolean;
  connecting: boolean;
  ws: WebSocket | null;
  messages: WebSocketMessage[];
  alerts: AlertMessage[];
  metrics: SystemMetrics | null;
  dockerData: DockerData | null;
  reconnectAttempts: number;
  maxReconnectAttempts: number;
  reconnectInterval: number;
  lastError: string | null;
  connect: () => void;
  disconnect: () => void;
  addAlert: (alert: Omit<AlertMessage, 'id' | 'timestamp' | 'read'>) => void;
  markAlertAsRead: (id: string) => void;
  clearAlerts: () => void;
  resetReconnectAttempts: () => void;
}

export const useWebSocketStore = create<WebSocketStore>()((set, get) => ({
  connected: false,
  connecting: false,
  ws: null,
  messages: [],
  alerts: [],
  metrics: null,
  dockerData: null,
  reconnectAttempts: 0,
  maxReconnectAttempts: 10,
  reconnectInterval: 1000, // Start with 1 second
  lastError: null,
  
  connect: () => {
    const state = get();

    // Don't connect if already connected or connecting
    if (state.connected || state.connecting) {
      return;
    }

    // Check if we've exceeded max reconnect attempts
    if (state.reconnectAttempts >= state.maxReconnectAttempts) {
      console.warn('Max reconnection attempts reached. Stopping reconnection.');
      set({
        lastError: 'Max reconnection attempts reached',
        connecting: false
      });
      return;
    }

    set({ connecting: true, lastError: null });

    try {
      const ws = new WebSocket('ws://localhost:8080');

      // Set a connection timeout
      const connectionTimeout = setTimeout(() => {
        if (ws.readyState === WebSocket.CONNECTING) {
          ws.close();
          console.error('WebSocket connection timeout');
          set({
            connecting: false,
            lastError: 'Connection timeout'
          });
          get().scheduleReconnect();
        }
      }, 10000); // 10 second timeout

      ws.onopen = () => {
        clearTimeout(connectionTimeout);
        console.log('WebSocket connected successfully');
        set({
          connected: true,
          connecting: false,
          ws,
          reconnectAttempts: 0,
          reconnectInterval: 1000,
          lastError: null
        });

        // Send ping to request initial data
        try {
          ws.send(JSON.stringify({ type: 'ping' }));
        } catch (error) {
          console.error('Failed to send ping:', error);
        }
      };
    
    ws.onmessage = (event) => {
      try {
        const message: WebSocketMessage = JSON.parse(event.data);
        
        switch (message.type) {
          case 'metrics':
            set({ metrics: message.data });
            break;
            
          case 'docker':
            set({ dockerData: message.data });
            break;
            
          case 'alert':
            const newAlert: AlertMessage = {
              ...message.data,
              read: false
            };
            set((state) => ({
              alerts: [newAlert, ...state.alerts].slice(0, 50)
            }));
            break;
            
          case 'error':
            console.error('WebSocket error:', message.data);
            break;
            
          default:
            console.log('Unknown message type:', message.type);
        }
        
        // Add to message history
        set((state) => ({
          messages: [message, ...state.messages].slice(0, 100)
        }));
      } catch (error) {
        console.error('Error parsing WebSocket message:', error);
      }
    };
    
      ws.onclose = (event) => {
        clearTimeout(connectionTimeout);
        console.log('WebSocket disconnected', { code: event.code, reason: event.reason });

        const wasConnected = get().connected;
        set({
          connected: false,
          connecting: false,
          ws: null,
          lastError: event.reason || `Connection closed (code: ${event.code})`
        });

        // Only attempt reconnection if we were previously connected or this wasn't a manual disconnect
        if (wasConnected || event.code !== 1000) {
          get().scheduleReconnect();
        }
      };

      ws.onerror = (error) => {
        clearTimeout(connectionTimeout);
        console.error('WebSocket error:', error);
        set({
          connected: false,
          connecting: false,
          lastError: 'WebSocket error occurred'
        });
      };
    
    set({ ws });
  },
  
  disconnect: () => {
    const { ws } = get();
    if (ws) {
      ws.close();
    }
    set({ connected: false, ws: null });
  },
  
  addAlert: (alert) => {
    const newAlert: AlertMessage = {
      ...alert,
      id: Date.now().toString(),
      timestamp: new Date().toISOString(),
      read: false,
    };
    set((state) => ({
      alerts: [newAlert, ...state.alerts].slice(0, 50),
    }));
  },
  
  markAlertAsRead: (id) => {
    set((state) => ({
      alerts: state.alerts.map((alert) =>
        alert.id === id ? { ...alert, read: true } : alert
      ),
    }));
  },
  
  clearAlerts: () => {
    set({ alerts: [] });
  },
}));

// Auto-connect when the store is created
if (typeof window !== 'undefined') {
  // Add a small delay to ensure the backend WebSocket server is ready
  setTimeout(() => {
    useWebSocketStore.getState().connect();
  }, 1000);
}
