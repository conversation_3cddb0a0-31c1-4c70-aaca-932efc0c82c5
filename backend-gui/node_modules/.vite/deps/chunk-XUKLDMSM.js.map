{"version": 3, "sources": ["../../refractor/lang/diff.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = diff\ndiff.displayName = 'diff'\ndiff.aliases = []\nfunction diff(Prism) {\n  ;(function (Prism) {\n    Prism.languages.diff = {\n      coord: [\n        // Match all kinds of coord lines (prefixed by \"+++\", \"---\" or \"***\").\n        /^(?:\\*{3}|-{3}|\\+{3}).*$/m, // Match \"@@ ... @@\" coord lines in unified diff.\n        /^@@.*@@$/m, // Match coord lines in normal diff (starts with a number).\n        /^\\d.*$/m\n      ] // deleted, inserted, unchanged, diff\n    }\n    /**\n     * A map from the name of a block to its line prefix.\n     *\n     * @type {Object<string, string>}\n     */\n    var PREFIXES = {\n      'deleted-sign': '-',\n      'deleted-arrow': '<',\n      'inserted-sign': '+',\n      'inserted-arrow': '>',\n      unchanged: ' ',\n      diff: '!'\n    } // add a token for each prefix\n    Object.keys(PREFIXES).forEach(function (name) {\n      var prefix = PREFIXES[name]\n      var alias = []\n      if (!/^\\w+$/.test(name)) {\n        // \"deleted-sign\" -> \"deleted\"\n        alias.push(/\\w+/.exec(name)[0])\n      }\n      if (name === 'diff') {\n        alias.push('bold')\n      }\n      Prism.languages.diff[name] = {\n        pattern: RegExp(\n          '^(?:[' + prefix + '].*(?:\\r\\n?|\\n|(?![\\\\s\\\\S])))+',\n          'm'\n        ),\n        alias: alias,\n        inside: {\n          line: {\n            pattern: /(.)(?=[\\s\\S]).*(?:\\r\\n?|\\n)?/,\n            lookbehind: true\n          },\n          prefix: {\n            pattern: /[\\s\\S]/,\n            alias: /\\w+/.exec(name)[0]\n          }\n        }\n      }\n    }) // make prefixes available to Diff plugin\n    Object.defineProperty(Prism.languages.diff, 'PREFIXES', {\n      value: PREFIXES\n    })\n  })(Prism)\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,SAAK,cAAc;AACnB,SAAK,UAAU,CAAC;AAChB,aAAS,KAAK,OAAO;AACnB;AAAC,OAAC,SAAUA,QAAO;AACjB,QAAAA,OAAM,UAAU,OAAO;AAAA,UACrB,OAAO;AAAA;AAAA,YAEL;AAAA;AAAA,YACA;AAAA;AAAA,YACA;AAAA,UACF;AAAA;AAAA,QACF;AAMA,YAAI,WAAW;AAAA,UACb,gBAAgB;AAAA,UAChB,iBAAiB;AAAA,UACjB,iBAAiB;AAAA,UACjB,kBAAkB;AAAA,UAClB,WAAW;AAAA,UACX,MAAM;AAAA,QACR;AACA,eAAO,KAAK,QAAQ,EAAE,QAAQ,SAAU,MAAM;AAC5C,cAAI,SAAS,SAAS,IAAI;AAC1B,cAAI,QAAQ,CAAC;AACb,cAAI,CAAC,QAAQ,KAAK,IAAI,GAAG;AAEvB,kBAAM,KAAK,MAAM,KAAK,IAAI,EAAE,CAAC,CAAC;AAAA,UAChC;AACA,cAAI,SAAS,QAAQ;AACnB,kBAAM,KAAK,MAAM;AAAA,UACnB;AACA,UAAAA,OAAM,UAAU,KAAK,IAAI,IAAI;AAAA,YAC3B,SAAS;AAAA,cACP,UAAU,SAAS;AAAA,cACnB;AAAA,YACF;AAAA,YACA;AAAA,YACA,QAAQ;AAAA,cACN,MAAM;AAAA,gBACJ,SAAS;AAAA,gBACT,YAAY;AAAA,cACd;AAAA,cACA,QAAQ;AAAA,gBACN,SAAS;AAAA,gBACT,OAAO,MAAM,KAAK,IAAI,EAAE,CAAC;AAAA,cAC3B;AAAA,YACF;AAAA,UACF;AAAA,QACF,CAAC;AACD,eAAO,eAAeA,OAAM,UAAU,MAAM,YAAY;AAAA,UACtD,OAAO;AAAA,QACT,CAAC;AAAA,MACH,GAAG,KAAK;AAAA,IACV;AAAA;AAAA;", "names": ["Prism"]}