{"version": 3, "sources": ["../../refractor/lang/http.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = http\nhttp.displayName = 'http'\nhttp.aliases = []\nfunction http(Prism) {\n  ;(function (Prism) {\n    /**\n     * @param {string} name\n     * @returns {RegExp}\n     */\n    function headerValueOf(name) {\n      return RegExp('(^(?:' + name + '):[ \\t]*(?![ \\t]))[^]+', 'i')\n    }\n    Prism.languages.http = {\n      'request-line': {\n        pattern:\n          /^(?:CONNECT|DELETE|GET|HEAD|OPTIONS|PATCH|POST|PRI|PUT|SEARCH|TRACE)\\s(?:https?:\\/\\/|\\/)\\S*\\sHTTP\\/[\\d.]+/m,\n        inside: {\n          // HTTP Method\n          method: {\n            pattern: /^[A-Z]+\\b/,\n            alias: 'property'\n          },\n          // Request Target e.g. http://example.com, /path/to/file\n          'request-target': {\n            pattern: /^(\\s)(?:https?:\\/\\/|\\/)\\S*(?=\\s)/,\n            lookbehind: true,\n            alias: 'url',\n            inside: Prism.languages.uri\n          },\n          // HTTP Version\n          'http-version': {\n            pattern: /^(\\s)HTTP\\/[\\d.]+/,\n            lookbehind: true,\n            alias: 'property'\n          }\n        }\n      },\n      'response-status': {\n        pattern: /^HTTP\\/[\\d.]+ \\d+ .+/m,\n        inside: {\n          // HTTP Version\n          'http-version': {\n            pattern: /^HTTP\\/[\\d.]+/,\n            alias: 'property'\n          },\n          // Status Code\n          'status-code': {\n            pattern: /^(\\s)\\d+(?=\\s)/,\n            lookbehind: true,\n            alias: 'number'\n          },\n          // Reason Phrase\n          'reason-phrase': {\n            pattern: /^(\\s).+/,\n            lookbehind: true,\n            alias: 'string'\n          }\n        }\n      },\n      header: {\n        pattern: /^[\\w-]+:.+(?:(?:\\r\\n?|\\n)[ \\t].+)*/m,\n        inside: {\n          'header-value': [\n            {\n              pattern: headerValueOf(/Content-Security-Policy/.source),\n              lookbehind: true,\n              alias: ['csp', 'languages-csp'],\n              inside: Prism.languages.csp\n            },\n            {\n              pattern: headerValueOf(/Public-Key-Pins(?:-Report-Only)?/.source),\n              lookbehind: true,\n              alias: ['hpkp', 'languages-hpkp'],\n              inside: Prism.languages.hpkp\n            },\n            {\n              pattern: headerValueOf(/Strict-Transport-Security/.source),\n              lookbehind: true,\n              alias: ['hsts', 'languages-hsts'],\n              inside: Prism.languages.hsts\n            },\n            {\n              pattern: headerValueOf(/[^:]+/.source),\n              lookbehind: true\n            }\n          ],\n          'header-name': {\n            pattern: /^[^:]+/,\n            alias: 'keyword'\n          },\n          punctuation: /^:/\n        }\n      }\n    } // Create a mapping of Content-Type headers to language definitions\n    var langs = Prism.languages\n    var httpLanguages = {\n      'application/javascript': langs.javascript,\n      'application/json': langs.json || langs.javascript,\n      'application/xml': langs.xml,\n      'text/xml': langs.xml,\n      'text/html': langs.html,\n      'text/css': langs.css,\n      'text/plain': langs.plain\n    } // Declare which types can also be suffixes\n    var suffixTypes = {\n      'application/json': true,\n      'application/xml': true\n    }\n    /**\n     * Returns a pattern for the given content type which matches it and any type which has it as a suffix.\n     *\n     * @param {string} contentType\n     * @returns {string}\n     */\n    function getSuffixPattern(contentType) {\n      var suffix = contentType.replace(/^[a-z]+\\//, '')\n      var suffixPattern = '\\\\w+/(?:[\\\\w.-]+\\\\+)+' + suffix + '(?![+\\\\w.-])'\n      return '(?:' + contentType + '|' + suffixPattern + ')'\n    } // Insert each content type parser that has its associated language\n    // currently loaded.\n    var options\n    for (var contentType in httpLanguages) {\n      if (httpLanguages[contentType]) {\n        options = options || {}\n        var pattern = suffixTypes[contentType]\n          ? getSuffixPattern(contentType)\n          : contentType\n        options[contentType.replace(/\\//g, '-')] = {\n          pattern: RegExp(\n            '(' +\n              /content-type:\\s*/.source +\n              pattern +\n              /(?:(?:\\r\\n?|\\n)[\\w-].*)*(?:\\r(?:\\n|(?!\\n))|\\n)/.source +\n              ')' + // This is a little interesting:\n              // The HTTP format spec required 1 empty line before the body to make everything unambiguous.\n              // However, when writing code by hand (e.g. to display on a website) people can forget about this,\n              // so we want to be liberal here. We will allow the empty line to be omitted if the first line of\n              // the body does not start with a [\\w-] character (as headers do).\n              /[^ \\t\\w-][\\s\\S]*/.source,\n            'i'\n          ),\n          lookbehind: true,\n          inside: httpLanguages[contentType]\n        }\n      }\n    }\n    if (options) {\n      Prism.languages.insertBefore('http', 'header', options)\n    }\n  })(Prism)\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,SAAK,cAAc;AACnB,SAAK,UAAU,CAAC;AAChB,aAAS,KAAK,OAAO;AACnB;AAAC,OAAC,SAAUA,QAAO;AAKjB,iBAAS,cAAc,MAAM;AAC3B,iBAAO,OAAO,UAAU,OAAO,wBAA0B,GAAG;AAAA,QAC9D;AACA,QAAAA,OAAM,UAAU,OAAO;AAAA,UACrB,gBAAgB;AAAA,YACd,SACE;AAAA,YACF,QAAQ;AAAA;AAAA,cAEN,QAAQ;AAAA,gBACN,SAAS;AAAA,gBACT,OAAO;AAAA,cACT;AAAA;AAAA,cAEA,kBAAkB;AAAA,gBAChB,SAAS;AAAA,gBACT,YAAY;AAAA,gBACZ,OAAO;AAAA,gBACP,QAAQA,OAAM,UAAU;AAAA,cAC1B;AAAA;AAAA,cAEA,gBAAgB;AAAA,gBACd,SAAS;AAAA,gBACT,YAAY;AAAA,gBACZ,OAAO;AAAA,cACT;AAAA,YACF;AAAA,UACF;AAAA,UACA,mBAAmB;AAAA,YACjB,SAAS;AAAA,YACT,QAAQ;AAAA;AAAA,cAEN,gBAAgB;AAAA,gBACd,SAAS;AAAA,gBACT,OAAO;AAAA,cACT;AAAA;AAAA,cAEA,eAAe;AAAA,gBACb,SAAS;AAAA,gBACT,YAAY;AAAA,gBACZ,OAAO;AAAA,cACT;AAAA;AAAA,cAEA,iBAAiB;AAAA,gBACf,SAAS;AAAA,gBACT,YAAY;AAAA,gBACZ,OAAO;AAAA,cACT;AAAA,YACF;AAAA,UACF;AAAA,UACA,QAAQ;AAAA,YACN,SAAS;AAAA,YACT,QAAQ;AAAA,cACN,gBAAgB;AAAA,gBACd;AAAA,kBACE,SAAS,cAAc,0BAA0B,MAAM;AAAA,kBACvD,YAAY;AAAA,kBACZ,OAAO,CAAC,OAAO,eAAe;AAAA,kBAC9B,QAAQA,OAAM,UAAU;AAAA,gBAC1B;AAAA,gBACA;AAAA,kBACE,SAAS,cAAc,mCAAmC,MAAM;AAAA,kBAChE,YAAY;AAAA,kBACZ,OAAO,CAAC,QAAQ,gBAAgB;AAAA,kBAChC,QAAQA,OAAM,UAAU;AAAA,gBAC1B;AAAA,gBACA;AAAA,kBACE,SAAS,cAAc,4BAA4B,MAAM;AAAA,kBACzD,YAAY;AAAA,kBACZ,OAAO,CAAC,QAAQ,gBAAgB;AAAA,kBAChC,QAAQA,OAAM,UAAU;AAAA,gBAC1B;AAAA,gBACA;AAAA,kBACE,SAAS,cAAc,QAAQ,MAAM;AAAA,kBACrC,YAAY;AAAA,gBACd;AAAA,cACF;AAAA,cACA,eAAe;AAAA,gBACb,SAAS;AAAA,gBACT,OAAO;AAAA,cACT;AAAA,cACA,aAAa;AAAA,YACf;AAAA,UACF;AAAA,QACF;AACA,YAAI,QAAQA,OAAM;AAClB,YAAI,gBAAgB;AAAA,UAClB,0BAA0B,MAAM;AAAA,UAChC,oBAAoB,MAAM,QAAQ,MAAM;AAAA,UACxC,mBAAmB,MAAM;AAAA,UACzB,YAAY,MAAM;AAAA,UAClB,aAAa,MAAM;AAAA,UACnB,YAAY,MAAM;AAAA,UAClB,cAAc,MAAM;AAAA,QACtB;AACA,YAAI,cAAc;AAAA,UAChB,oBAAoB;AAAA,UACpB,mBAAmB;AAAA,QACrB;AAOA,iBAAS,iBAAiBC,cAAa;AACrC,cAAI,SAASA,aAAY,QAAQ,aAAa,EAAE;AAChD,cAAI,gBAAgB,0BAA0B,SAAS;AACvD,iBAAO,QAAQA,eAAc,MAAM,gBAAgB;AAAA,QACrD;AAEA,YAAI;AACJ,iBAAS,eAAe,eAAe;AACrC,cAAI,cAAc,WAAW,GAAG;AAC9B,sBAAU,WAAW,CAAC;AACtB,gBAAI,UAAU,YAAY,WAAW,IACjC,iBAAiB,WAAW,IAC5B;AACJ,oBAAQ,YAAY,QAAQ,OAAO,GAAG,CAAC,IAAI;AAAA,cACzC,SAAS;AAAA,gBACP,MACE,mBAAmB,SACnB,UACA,iDAAiD,SACjD;AAAA;AAAA;AAAA;AAAA;AAAA,gBAKA,mBAAmB;AAAA,gBACrB;AAAA,cACF;AAAA,cACA,YAAY;AAAA,cACZ,QAAQ,cAAc,WAAW;AAAA,YACnC;AAAA,UACF;AAAA,QACF;AACA,YAAI,SAAS;AACX,UAAAD,OAAM,UAAU,aAAa,QAAQ,UAAU,OAAO;AAAA,QACxD;AAAA,MACF,GAAG,KAAK;AAAA,IACV;AAAA;AAAA;", "names": ["Prism", "contentType"]}