{"version": 3, "sources": ["../../highlight.js/lib/languages/roboconf.js"], "sourcesContent": ["/*\nLanguage: Roboconf\nAuthor: <PERSON> <v<PERSON><PERSON><PERSON>@linagora.com>\nDescription: Syntax highlighting for Roboconf's DSL\nWebsite: http://roboconf.net\nCategory: config\n*/\n\nfunction roboconf(hljs) {\n  const IDENTIFIER = '[a-zA-Z-_][^\\\\n{]+\\\\{';\n\n  const PROPERTY = {\n    className: 'attribute',\n    begin: /[a-zA-Z-_]+/,\n    end: /\\s*:/,\n    excludeEnd: true,\n    starts: {\n      end: ';',\n      relevance: 0,\n      contains: [\n        {\n          className: 'variable',\n          begin: /\\.[a-zA-Z-_]+/\n        },\n        {\n          className: 'keyword',\n          begin: /\\(optional\\)/\n        }\n      ]\n    }\n  };\n\n  return {\n    name: 'Roboconf',\n    aliases: [\n      'graph',\n      'instances'\n    ],\n    case_insensitive: true,\n    keywords: 'import',\n    contains: [\n      // Facet sections\n      {\n        begin: '^facet ' + IDENTIFIER,\n        end: /\\}/,\n        keywords: 'facet',\n        contains: [\n          PROPERTY,\n          hljs.HASH_COMMENT_MODE\n        ]\n      },\n\n      // Instance sections\n      {\n        begin: '^\\\\s*instance of ' + IDENTIFIER,\n        end: /\\}/,\n        keywords: 'name count channels instance-data instance-state instance of',\n        illegal: /\\S/,\n        contains: [\n          'self',\n          PROPERTY,\n          hljs.HASH_COMMENT_MODE\n        ]\n      },\n\n      // Component sections\n      {\n        begin: '^' + IDENTIFIER,\n        end: /\\}/,\n        contains: [\n          PROPERTY,\n          hljs.HASH_COMMENT_MODE\n        ]\n      },\n\n      // Comments\n      hljs.HASH_COMMENT_MODE\n    ]\n  };\n}\n\nmodule.exports = roboconf;\n"], "mappings": ";;;;;AAAA;AAAA;AAQA,aAAS,SAAS,MAAM;AACtB,YAAM,aAAa;AAEnB,YAAM,WAAW;AAAA,QACf,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,YAAY;AAAA,QACZ,QAAQ;AAAA,UACN,KAAK;AAAA,UACL,WAAW;AAAA,UACX,UAAU;AAAA,YACR;AAAA,cACE,WAAW;AAAA,cACX,OAAO;AAAA,YACT;AAAA,YACA;AAAA,cACE,WAAW;AAAA,cACX,OAAO;AAAA,YACT;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAEA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS;AAAA,UACP;AAAA,UACA;AAAA,QACF;AAAA,QACA,kBAAkB;AAAA,QAClB,UAAU;AAAA,QACV,UAAU;AAAA;AAAA,UAER;AAAA,YACE,OAAO,YAAY;AAAA,YACnB,KAAK;AAAA,YACL,UAAU;AAAA,YACV,UAAU;AAAA,cACR;AAAA,cACA,KAAK;AAAA,YACP;AAAA,UACF;AAAA;AAAA,UAGA;AAAA,YACE,OAAO,sBAAsB;AAAA,YAC7B,KAAK;AAAA,YACL,UAAU;AAAA,YACV,SAAS;AAAA,YACT,UAAU;AAAA,cACR;AAAA,cACA;AAAA,cACA,KAAK;AAAA,YACP;AAAA,UACF;AAAA;AAAA,UAGA;AAAA,YACE,OAAO,MAAM;AAAA,YACb,KAAK;AAAA,YACL,UAAU;AAAA,cACR;AAAA,cACA,KAAK;AAAA,YACP;AAAA,UACF;AAAA;AAAA,UAGA,KAAK;AAAA,QACP;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}