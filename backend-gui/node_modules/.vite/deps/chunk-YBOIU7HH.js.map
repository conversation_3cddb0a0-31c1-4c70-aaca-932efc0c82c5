{"version": 3, "sources": ["../../refractor/lang/tremor.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = tremor\ntremor.displayName = 'tremor'\ntremor.aliases = []\nfunction tremor(Prism) {\n  ;(function (Prism) {\n    Prism.languages.tremor = {\n      comment: {\n        pattern: /(^|[^\\\\])(?:\\/\\*[\\s\\S]*?\\*\\/|(?:--|\\/\\/|#).*)/,\n        lookbehind: true\n      },\n      'interpolated-string': null,\n      // see below\n      extractor: {\n        pattern: /\\b[a-z_]\\w*\\|(?:[^\\r\\n\\\\|]|\\\\(?:\\r\\n|[\\s\\S]))*\\|/i,\n        greedy: true,\n        inside: {\n          regex: {\n            pattern: /(^re)\\|[\\s\\S]+/,\n            lookbehind: true\n          },\n          function: /^\\w+/,\n          value: /\\|[\\s\\S]+/\n        }\n      },\n      identifier: {\n        pattern: /`[^`]*`/,\n        greedy: true\n      },\n      function: /\\b[a-z_]\\w*(?=\\s*(?:::\\s*<|\\())\\b/,\n      keyword:\n        /\\b(?:args|as|by|case|config|connect|connector|const|copy|create|default|define|deploy|drop|each|emit|end|erase|event|flow|fn|for|from|group|having|insert|into|intrinsic|let|links|match|merge|mod|move|of|operator|patch|pipeline|recur|script|select|set|sliding|state|stream|to|tumbling|update|use|when|where|window|with)\\b/,\n      boolean: /\\b(?:false|null|true)\\b/i,\n      number:\n        /\\b(?:0b[01_]*|0x[0-9a-fA-F_]*|\\d[\\d_]*(?:\\.\\d[\\d_]*)?(?:[Ee][+-]?[\\d_]+)?)\\b/,\n      'pattern-punctuation': {\n        pattern: /%(?=[({[])/,\n        alias: 'punctuation'\n      },\n      operator:\n        /[-+*\\/%~!^]=?|=[=>]?|&[&=]?|\\|[|=]?|<<?=?|>>?>?=?|(?:absent|and|not|or|present|xor)\\b/,\n      punctuation: /::|[;\\[\\]()\\{\\},.:]/\n    }\n    var interpolationPattern =\n      /#\\{(?:[^\"{}]|\\{[^{}]*\\}|\"(?:[^\"\\\\\\r\\n]|\\\\(?:\\r\\n|[\\s\\S]))*\")*\\}/.source\n    Prism.languages.tremor['interpolated-string'] = {\n      pattern: RegExp(\n        /(^|[^\\\\])/.source +\n          '(?:' +\n          '\"\"\"(?:' +\n          /[^\"\\\\#]|\\\\[\\s\\S]|\"(?!\"\")|#(?!\\{)/.source +\n          '|' +\n          interpolationPattern +\n          ')*\"\"\"' +\n          '|' +\n          '\"(?:' +\n          /[^\"\\\\\\r\\n#]|\\\\(?:\\r\\n|[\\s\\S])|#(?!\\{)/.source +\n          '|' +\n          interpolationPattern +\n          ')*\"' +\n          ')'\n      ),\n      lookbehind: true,\n      greedy: true,\n      inside: {\n        interpolation: {\n          pattern: RegExp(interpolationPattern),\n          inside: {\n            punctuation: /^#\\{|\\}$/,\n            expression: {\n              pattern: /[\\s\\S]+/,\n              inside: Prism.languages.tremor\n            }\n          }\n        },\n        string: /[\\s\\S]+/\n      }\n    }\n    Prism.languages.troy = Prism.languages['tremor']\n    Prism.languages.trickle = Prism.languages['tremor']\n  })(Prism)\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,WAAO,cAAc;AACrB,WAAO,UAAU,CAAC;AAClB,aAAS,OAAO,OAAO;AACrB;AAAC,OAAC,SAAUA,QAAO;AACjB,QAAAA,OAAM,UAAU,SAAS;AAAA,UACvB,SAAS;AAAA,YACP,SAAS;AAAA,YACT,YAAY;AAAA,UACd;AAAA,UACA,uBAAuB;AAAA;AAAA,UAEvB,WAAW;AAAA,YACT,SAAS;AAAA,YACT,QAAQ;AAAA,YACR,QAAQ;AAAA,cACN,OAAO;AAAA,gBACL,SAAS;AAAA,gBACT,YAAY;AAAA,cACd;AAAA,cACA,UAAU;AAAA,cACV,OAAO;AAAA,YACT;AAAA,UACF;AAAA,UACA,YAAY;AAAA,YACV,SAAS;AAAA,YACT,QAAQ;AAAA,UACV;AAAA,UACA,UAAU;AAAA,UACV,SACE;AAAA,UACF,SAAS;AAAA,UACT,QACE;AAAA,UACF,uBAAuB;AAAA,YACrB,SAAS;AAAA,YACT,OAAO;AAAA,UACT;AAAA,UACA,UACE;AAAA,UACF,aAAa;AAAA,QACf;AACA,YAAI,uBACF,kEAAkE;AACpE,QAAAA,OAAM,UAAU,OAAO,qBAAqB,IAAI;AAAA,UAC9C,SAAS;AAAA,YACP,YAAY,SACV,cAEA,mCAAmC,SACnC,MACA,uBACA,eAGA,wCAAwC,SACxC,MACA,uBACA;AAAA,UAEJ;AAAA,UACA,YAAY;AAAA,UACZ,QAAQ;AAAA,UACR,QAAQ;AAAA,YACN,eAAe;AAAA,cACb,SAAS,OAAO,oBAAoB;AAAA,cACpC,QAAQ;AAAA,gBACN,aAAa;AAAA,gBACb,YAAY;AAAA,kBACV,SAAS;AAAA,kBACT,QAAQA,OAAM,UAAU;AAAA,gBAC1B;AAAA,cACF;AAAA,YACF;AAAA,YACA,QAAQ;AAAA,UACV;AAAA,QACF;AACA,QAAAA,OAAM,UAAU,OAAOA,OAAM,UAAU,QAAQ;AAC/C,QAAAA,OAAM,UAAU,UAAUA,OAAM,UAAU,QAAQ;AAAA,MACpD,GAAG,KAAK;AAAA,IACV;AAAA;AAAA;", "names": ["Prism"]}