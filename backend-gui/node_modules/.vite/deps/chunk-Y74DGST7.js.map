{"version": 3, "sources": ["../../highlight.js/lib/languages/haskell.js"], "sourcesContent": ["/*\nLanguage: Haskell\nAuthor: <PERSON> <<EMAIL>>\nContributors: <PERSON><PERSON> <<EMAIL>>\nWebsite: https://www.haskell.org\nCategory: functional\n*/\n\nfunction haskell(hljs) {\n  const COMMENT = {\n    variants: [\n      hljs.COMMENT('--', '$'),\n      hljs.COMMENT(\n        /\\{-/,\n        /-\\}/,\n        {\n          contains: ['self']\n        }\n      )\n    ]\n  };\n\n  const PRAGMA = {\n    className: 'meta',\n    begin: /\\{-#/,\n    end: /#-\\}/\n  };\n\n  const PREPROCESSOR = {\n    className: 'meta',\n    begin: '^#',\n    end: '$'\n  };\n\n  const CONSTRUCTOR = {\n    className: 'type',\n    begin: '\\\\b[A-Z][\\\\w\\']*', // TODO: other constructors (build-in, infix).\n    relevance: 0\n  };\n\n  const LIST = {\n    begin: '\\\\(',\n    end: '\\\\)',\n    illegal: '\"',\n    contains: [\n      PRAGMA,\n      PREPROCESSOR,\n      {\n        className: 'type',\n        begin: '\\\\b[A-Z][\\\\w]*(\\\\((\\\\.\\\\.|,|\\\\w+)\\\\))?'\n      },\n      hljs.inherit(hljs.TITLE_MODE, {\n        begin: '[_a-z][\\\\w\\']*'\n      }),\n      COMMENT\n    ]\n  };\n\n  const RECORD = {\n    begin: /\\{/,\n    end: /\\}/,\n    contains: LIST.contains\n  };\n\n  return {\n    name: 'Haskell',\n    aliases: ['hs'],\n    keywords:\n      'let in if then else case of where do module import hiding ' +\n      'qualified type data newtype deriving class instance as default ' +\n      'infix infixl infixr foreign export ccall stdcall cplusplus ' +\n      'jvm dotnet safe unsafe family forall mdo proc rec',\n    contains: [\n      // Top-level constructions.\n      {\n        beginKeywords: 'module',\n        end: 'where',\n        keywords: 'module where',\n        contains: [\n          LIST,\n          COMMENT\n        ],\n        illegal: '\\\\W\\\\.|;'\n      },\n      {\n        begin: '\\\\bimport\\\\b',\n        end: '$',\n        keywords: 'import qualified as hiding',\n        contains: [\n          LIST,\n          COMMENT\n        ],\n        illegal: '\\\\W\\\\.|;'\n      },\n      {\n        className: 'class',\n        begin: '^(\\\\s*)?(class|instance)\\\\b',\n        end: 'where',\n        keywords: 'class family instance where',\n        contains: [\n          CONSTRUCTOR,\n          LIST,\n          COMMENT\n        ]\n      },\n      {\n        className: 'class',\n        begin: '\\\\b(data|(new)?type)\\\\b',\n        end: '$',\n        keywords: 'data family type newtype deriving',\n        contains: [\n          PRAGMA,\n          CONSTRUCTOR,\n          LIST,\n          RECORD,\n          COMMENT\n        ]\n      },\n      {\n        beginKeywords: 'default',\n        end: '$',\n        contains: [\n          CONSTRUCTOR,\n          LIST,\n          COMMENT\n        ]\n      },\n      {\n        beginKeywords: 'infix infixl infixr',\n        end: '$',\n        contains: [\n          hljs.C_NUMBER_MODE,\n          COMMENT\n        ]\n      },\n      {\n        begin: '\\\\bforeign\\\\b',\n        end: '$',\n        keywords: 'foreign import export ccall stdcall cplusplus jvm ' +\n                  'dotnet safe unsafe',\n        contains: [\n          CONSTRUCTOR,\n          hljs.QUOTE_STRING_MODE,\n          COMMENT\n        ]\n      },\n      {\n        className: 'meta',\n        begin: '#!\\\\/usr\\\\/bin\\\\/env\\ runhaskell',\n        end: '$'\n      },\n      // \"Whitespaces\".\n      PRAGMA,\n      PREPROCESSOR,\n\n      // Literals and names.\n\n      // TODO: characters.\n      hljs.QUOTE_STRING_MODE,\n      hljs.C_NUMBER_MODE,\n      CONSTRUCTOR,\n      hljs.inherit(hljs.TITLE_MODE, {\n        begin: '^[_a-z][\\\\w\\']*'\n      }),\n      COMMENT,\n      { // No markup, relevance booster\n        begin: '->|<-'\n      }\n    ]\n  };\n}\n\nmodule.exports = haskell;\n"], "mappings": ";;;;;AAAA;AAAA;AAQA,aAAS,QAAQ,MAAM;AACrB,YAAM,UAAU;AAAA,QACd,UAAU;AAAA,UACR,KAAK,QAAQ,MAAM,GAAG;AAAA,UACtB,KAAK;AAAA,YACH;AAAA,YACA;AAAA,YACA;AAAA,cACE,UAAU,CAAC,MAAM;AAAA,YACnB;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAEA,YAAM,SAAS;AAAA,QACb,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,MACP;AAEA,YAAM,eAAe;AAAA,QACnB,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,MACP;AAEA,YAAM,cAAc;AAAA,QAClB,WAAW;AAAA,QACX,OAAO;AAAA;AAAA,QACP,WAAW;AAAA,MACb;AAEA,YAAM,OAAO;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,SAAS;AAAA,QACT,UAAU;AAAA,UACR;AAAA,UACA;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,UACT;AAAA,UACA,KAAK,QAAQ,KAAK,YAAY;AAAA,YAC5B,OAAO;AAAA,UACT,CAAC;AAAA,UACD;AAAA,QACF;AAAA,MACF;AAEA,YAAM,SAAS;AAAA,QACb,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU,KAAK;AAAA,MACjB;AAEA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS,CAAC,IAAI;AAAA,QACd,UACE;AAAA,QAIF,UAAU;AAAA;AAAA,UAER;AAAA,YACE,eAAe;AAAA,YACf,KAAK;AAAA,YACL,UAAU;AAAA,YACV,UAAU;AAAA,cACR;AAAA,cACA;AAAA,YACF;AAAA,YACA,SAAS;AAAA,UACX;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU;AAAA,YACV,UAAU;AAAA,cACR;AAAA,cACA;AAAA,YACF;AAAA,YACA,SAAS;AAAA,UACX;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU;AAAA,YACV,UAAU;AAAA,cACR;AAAA,cACA;AAAA,cACA;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU;AAAA,YACV,UAAU;AAAA,cACR;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,YACE,eAAe;AAAA,YACf,KAAK;AAAA,YACL,UAAU;AAAA,cACR;AAAA,cACA;AAAA,cACA;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,YACE,eAAe;AAAA,YACf,KAAK;AAAA,YACL,UAAU;AAAA,cACR,KAAK;AAAA,cACL;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU;AAAA,YAEV,UAAU;AAAA,cACR;AAAA,cACA,KAAK;AAAA,cACL;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA;AAAA,UAEA;AAAA,UACA;AAAA;AAAA;AAAA,UAKA,KAAK;AAAA,UACL,KAAK;AAAA,UACL;AAAA,UACA,KAAK,QAAQ,KAAK,YAAY;AAAA,YAC5B,OAAO;AAAA,UACT,CAAC;AAAA,UACD;AAAA,UACA;AAAA;AAAA,YACE,OAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}