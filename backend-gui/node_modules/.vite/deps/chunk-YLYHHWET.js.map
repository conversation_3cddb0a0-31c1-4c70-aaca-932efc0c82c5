{"version": 3, "sources": ["../../highlight.js/lib/languages/moonscript.js"], "sourcesContent": ["/*\nLanguage: MoonScript\nAuthor: <PERSON> <<EMAIL>>\nDescription: MoonScript is a programming language that transcompiles to Lua.\nOrigin: coffeescript.js\nWebsite: http://moonscript.org/\nCategory: scripting\n*/\n\nfunction moonscript(hljs) {\n  const KEYWORDS = {\n    keyword:\n      // Moonscript keywords\n      'if then not for in while do return else elseif break continue switch and or ' +\n      'unless when class extends super local import export from using',\n    literal:\n      'true false nil',\n    built_in:\n      '_G _VERSION assert collectgarbage dofile error getfenv getmetatable ipairs load ' +\n      'loadfile loadstring module next pairs pcall print rawequal rawget rawset require ' +\n      'select setfenv setmetatable tonumber tostring type unpack xpcall coroutine debug ' +\n      'io math os package string table'\n  };\n  const JS_IDENT_RE = '[A-Za-z$_][0-9A-Za-z$_]*';\n  const SUBST = {\n    className: 'subst',\n    begin: /#\\{/,\n    end: /\\}/,\n    keywords: KEYWORDS\n  };\n  const EXPRESSIONS = [\n    hljs.inherit(hljs.C_NUMBER_MODE,\n      {\n        starts: {\n          end: '(\\\\s*/)?',\n          relevance: 0\n        }\n      }), // a number tries to eat the following slash to prevent treating it as a regexp\n    {\n      className: 'string',\n      variants: [\n        {\n          begin: /'/,\n          end: /'/,\n          contains: [ hljs.BACKSLASH_ESCAPE ]\n        },\n        {\n          begin: /\"/,\n          end: /\"/,\n          contains: [\n            hljs.BACKSLASH_ESCAPE,\n            SUBST\n          ]\n        }\n      ]\n    },\n    {\n      className: 'built_in',\n      begin: '@__' + hljs.IDENT_RE\n    },\n    {\n      begin: '@' + hljs.IDENT_RE // relevance booster on par with CoffeeScript\n    },\n    {\n      begin: hljs.IDENT_RE + '\\\\\\\\' + hljs.IDENT_RE // inst\\method\n    }\n  ];\n  SUBST.contains = EXPRESSIONS;\n\n  const TITLE = hljs.inherit(hljs.TITLE_MODE, {\n    begin: JS_IDENT_RE\n  });\n  const POSSIBLE_PARAMS_RE = '(\\\\(.*\\\\)\\\\s*)?\\\\B[-=]>';\n  const PARAMS = {\n    className: 'params',\n    begin: '\\\\([^\\\\(]',\n    returnBegin: true,\n    /* We need another contained nameless mode to not have every nested\n    pair of parens to be called \"params\" */\n    contains: [\n      {\n        begin: /\\(/,\n        end: /\\)/,\n        keywords: KEYWORDS,\n        contains: [ 'self' ].concat(EXPRESSIONS)\n      }\n    ]\n  };\n\n  return {\n    name: 'MoonScript',\n    aliases: [ 'moon' ],\n    keywords: KEYWORDS,\n    illegal: /\\/\\*/,\n    contains: EXPRESSIONS.concat([\n      hljs.COMMENT('--', '$'),\n      {\n        className: 'function', // function: -> =>\n        begin: '^\\\\s*' + JS_IDENT_RE + '\\\\s*=\\\\s*' + POSSIBLE_PARAMS_RE,\n        end: '[-=]>',\n        returnBegin: true,\n        contains: [\n          TITLE,\n          PARAMS\n        ]\n      },\n      {\n        begin: /[\\(,:=]\\s*/, // anonymous function start\n        relevance: 0,\n        contains: [\n          {\n            className: 'function',\n            begin: POSSIBLE_PARAMS_RE,\n            end: '[-=]>',\n            returnBegin: true,\n            contains: [ PARAMS ]\n          }\n        ]\n      },\n      {\n        className: 'class',\n        beginKeywords: 'class',\n        end: '$',\n        illegal: /[:=\"\\[\\]]/,\n        contains: [\n          {\n            beginKeywords: 'extends',\n            endsWithParent: true,\n            illegal: /[:=\"\\[\\]]/,\n            contains: [ TITLE ]\n          },\n          TITLE\n        ]\n      },\n      {\n        className: 'name', // table\n        begin: JS_IDENT_RE + ':',\n        end: ':',\n        returnBegin: true,\n        returnEnd: true,\n        relevance: 0\n      }\n    ])\n  };\n}\n\nmodule.exports = moonscript;\n"], "mappings": ";;;;;AAAA;AAAA;AASA,aAAS,WAAW,MAAM;AACxB,YAAM,WAAW;AAAA,QACf;AAAA;AAAA,UAEE;AAAA;AAAA,QAEF,SACE;AAAA,QACF,UACE;AAAA,MAIJ;AACA,YAAM,cAAc;AACpB,YAAM,QAAQ;AAAA,QACZ,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU;AAAA,MACZ;AACA,YAAM,cAAc;AAAA,QAClB,KAAK;AAAA,UAAQ,KAAK;AAAA,UAChB;AAAA,YACE,QAAQ;AAAA,cACN,KAAK;AAAA,cACL,WAAW;AAAA,YACb;AAAA,UACF;AAAA,QAAC;AAAA;AAAA,QACH;AAAA,UACE,WAAW;AAAA,UACX,UAAU;AAAA,YACR;AAAA,cACE,OAAO;AAAA,cACP,KAAK;AAAA,cACL,UAAU,CAAE,KAAK,gBAAiB;AAAA,YACpC;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,KAAK;AAAA,cACL,UAAU;AAAA,gBACR,KAAK;AAAA,gBACL;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,QACA;AAAA,UACE,WAAW;AAAA,UACX,OAAO,QAAQ,KAAK;AAAA,QACtB;AAAA,QACA;AAAA,UACE,OAAO,MAAM,KAAK;AAAA;AAAA,QACpB;AAAA,QACA;AAAA,UACE,OAAO,KAAK,WAAW,SAAS,KAAK;AAAA;AAAA,QACvC;AAAA,MACF;AACA,YAAM,WAAW;AAEjB,YAAM,QAAQ,KAAK,QAAQ,KAAK,YAAY;AAAA,QAC1C,OAAO;AAAA,MACT,CAAC;AACD,YAAM,qBAAqB;AAC3B,YAAM,SAAS;AAAA,QACb,WAAW;AAAA,QACX,OAAO;AAAA,QACP,aAAa;AAAA;AAAA;AAAA,QAGb,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU;AAAA,YACV,UAAU,CAAE,MAAO,EAAE,OAAO,WAAW;AAAA,UACzC;AAAA,QACF;AAAA,MACF;AAEA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS,CAAE,MAAO;AAAA,QAClB,UAAU;AAAA,QACV,SAAS;AAAA,QACT,UAAU,YAAY,OAAO;AAAA,UAC3B,KAAK,QAAQ,MAAM,GAAG;AAAA,UACtB;AAAA,YACE,WAAW;AAAA;AAAA,YACX,OAAO,UAAU,cAAc,cAAc;AAAA,YAC7C,KAAK;AAAA,YACL,aAAa;AAAA,YACb,UAAU;AAAA,cACR;AAAA,cACA;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,YACE,OAAO;AAAA;AAAA,YACP,WAAW;AAAA,YACX,UAAU;AAAA,cACR;AAAA,gBACE,WAAW;AAAA,gBACX,OAAO;AAAA,gBACP,KAAK;AAAA,gBACL,aAAa;AAAA,gBACb,UAAU,CAAE,MAAO;AAAA,cACrB;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,eAAe;AAAA,YACf,KAAK;AAAA,YACL,SAAS;AAAA,YACT,UAAU;AAAA,cACR;AAAA,gBACE,eAAe;AAAA,gBACf,gBAAgB;AAAA,gBAChB,SAAS;AAAA,gBACT,UAAU,CAAE,KAAM;AAAA,cACpB;AAAA,cACA;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,YACE,WAAW;AAAA;AAAA,YACX,OAAO,cAAc;AAAA,YACrB,KAAK;AAAA,YACL,aAAa;AAAA,YACb,WAAW;AAAA,YACX,WAAW;AAAA,UACb;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}