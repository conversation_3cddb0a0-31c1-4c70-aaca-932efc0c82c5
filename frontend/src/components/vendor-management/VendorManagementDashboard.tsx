import React, { useState, useEffect } from 'react';
import { Card, Badge, Group, Text, Button, Stack, Tabs, ActionIcon, Tooltip, Progress, Select, TextInput, Grid, Container, Title, Divider } from '@mantine/core';
import { 
  IconSearch, 
  IconPlus, 
  IconFilter, 
  IconBuilding, 
  IconUsers, 
  IconTrendingUp, 
  IconCalendar, 
  IconCurrencyDollar, 
  IconAlertTriangle,
  IconCheck,
  IconClock,
  IconFileText,
  IconMessage,
  IconStar,
  IconChartBar,
  IconSettings
} from '@tabler/icons-react';
import { VendorAnalytics, Vendor, VendorProject, VendorProposal, VendorInvoice, VendorCommunication } from '../../types/vendor-management';
import { VendorList } from './VendorList';
import { ProjectList } from './ProjectList';
import { ProposalList } from './ProposalList';
import { InvoiceList } from './InvoiceList';
import { CommunicationList } from './CommunicationList';
import { VendorAnalyticsDashboard } from './VendorAnalyticsDashboard';
import { CreateVendorModal } from './CreateVendorModal';
import { notifications } from '@mantine/notifications';

interface VendorManagementDashboardProps {
  className?: string;
}

// Mock data for demonstration - replace with actual API calls
const mockAnalytics: VendorAnalytics = {
  totalVendors: 45,
  activeVendors: 38,
  totalProjects: 127,
  activeProjects: 23,
  totalSpent: 2450000,
  averageProjectValue: 85000,
  topPerformingVendors: [],
  upcomingDeadlines: [
    {
      type: 'contract_expiry',
      item: { vendorName: 'TechCorp Solutions', contractDate: '2024-07-15' },
      dueDate: '2024-07-15',
      daysUntilDue: 26
    },
    {
      type: 'project_deadline',
      item: { projectName: 'Leadership Development Program', vendorName: 'ExecTraining Ltd' },
      dueDate: '2024-07-01',
      daysUntilDue: 12
    },
    {
      type: 'invoice_due',
      item: { invoiceNumber: 'INV-2024-0845', amount: 15000, vendorName: 'SkillBuilder Academy' },
      dueDate: '2024-06-25',
      daysUntilDue: 6
    }
  ],
  spendingByCategory: [
    { category: 'Leadership Training', amount: 750000, percentage: 30.6 },
    { category: 'Technical Skills', amount: 612000, percentage: 25.0 },
    { category: 'Compliance Training', amount: 441000, percentage: 18.0 },
    { category: 'Soft Skills', amount: 367500, percentage: 15.0 },
    { category: 'Assessment Tools', amount: 279500, percentage: 11.4 }
  ],
  performanceTrends: [
    { period: '2024-Q1', onTimeDelivery: 92, budgetAdherence: 96, qualityScore: 8.7 },
    { period: '2024-Q2', onTimeDelivery: 89, budgetAdherence: 94, qualityScore: 8.9 },
    { period: '2024-Q3', onTimeDelivery: 94, budgetAdherence: 98, qualityScore: 9.1 }
  ]
};

export const VendorManagementDashboard: React.FC<VendorManagementDashboardProps> = ({ className }) => {
  const [activeTab, setActiveTab] = useState<string>('dashboard');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilter, setSelectedFilter] = useState<string | null>('all');
  const [analytics, setAnalytics] = useState<VendorAnalytics>(mockAnalytics);
  const [createVendorModalOpen, setCreateVendorModalOpen] = useState(false);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    // Load initial data
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    setLoading(true);
    try {
      // TODO: Replace with actual API calls
      // const data = await vendorService.getAnalytics();
      // setAnalytics(data);
      
      // For now, using mock data
      setTimeout(() => {
        setAnalytics(mockAnalytics);
        setLoading(false);
      }, 1000);
    } catch (error) {
      notifications.show({
        title: 'Error',
        message: 'Failed to load dashboard data',
        color: 'red'
      });
      setLoading(false);
    }
  };

  const handleCreateVendor = () => {
    setCreateVendorModalOpen(true);
  };

  const handleVendorCreated = () => {
    setCreateVendorModalOpen(false);
    loadDashboardData();
    notifications.show({
      title: 'Success',
      message: 'Vendor created successfully',
      color: 'green'
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'green';
      case 'pending': return 'yellow';
      case 'suspended': return 'red';
      case 'inactive': return 'gray';
      default: return 'blue';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'red';
      case 'high': return 'orange';
      case 'medium': return 'yellow';
      case 'low': return 'green';
      default: return 'blue';
    }
  };

  return (
    <Container size="xl" className={className}>
      <Stack gap="md">
        {/* Header Section */}
        <Group justify="space-between" align="center">
          <div>
            <Title order={2} c="dark.8">
              <Group gap="sm" align="center">
                <IconBuilding size={28} />
                Vendor Management
              </Group>
            </Title>
            <Text c="dimmed" size="sm" mt={4}>
              Manage vendors, projects, proposals, invoices, and communications for L&D operations
            </Text>
          </div>
          <Group gap="sm">
            <Button 
              leftSection={<IconPlus size={16} />} 
              onClick={handleCreateVendor}
              variant="filled"
            >
              Add Vendor
            </Button>
            <ActionIcon variant="light" size="lg">
              <IconSettings size={18} />
            </ActionIcon>
          </Group>
        </Group>

        {/* Quick Stats Cards */}
        <Grid>
          <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
            <Card withBorder padding="lg" radius="md">
              <Group justify="space-between" align="flex-start" mb="xs">
                <Text size="sm" c="dimmed" fw={600}>Total Vendors</Text>
                <IconBuilding size={20} color="#4CAF50" />
              </Group>
              <Text size="xl" fw={700}>{analytics.totalVendors}</Text>
              <Text size="xs" c="dimmed">
                <Text component="span" c="green" fw={500}>
                  {analytics.activeVendors} active
                </Text>
                {' • '}
                {analytics.totalVendors - analytics.activeVendors} inactive
              </Text>
            </Card>
          </Grid.Col>

          <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
            <Card withBorder padding="lg" radius="md">
              <Group justify="space-between" align="flex-start" mb="xs">
                <Text size="sm" c="dimmed" fw={600}>Active Projects</Text>
                <IconChartBar size={20} color="#2196F3" />
              </Group>
              <Text size="xl" fw={700}>{analytics.activeProjects}</Text>
              <Text size="xs" c="dimmed">
                <Text component="span" c="blue" fw={500}>
                  {analytics.totalProjects} total
                </Text>
                {' projects managed'}
              </Text>
            </Card>
          </Grid.Col>

          <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
            <Card withBorder padding="lg" radius="md">
              <Group justify="space-between" align="flex-start" mb="xs">
                <Text size="sm" c="dimmed" fw={600}>Total Spent</Text>
                <IconCurrencyDollar size={20} color="#FF9800" />
              </Group>
              <Text size="xl" fw={700}>${(analytics.totalSpent / 1000000).toFixed(2)}M</Text>
              <Text size="xs" c="dimmed">
                <Text component="span" c="orange" fw={500}>
                  ${(analytics.averageProjectValue / 1000).toFixed(0)}K
                </Text>
                {' avg per project'}
              </Text>
            </Card>
          </Grid.Col>

          <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
            <Card withBorder padding="lg" radius="md">
              <Group justify="space-between" align="flex-start" mb="xs">
                <Text size="sm" c="dimmed" fw={600}>Upcoming Deadlines</Text>
                <IconAlertTriangle size={20} color="#F44336" />
              </Group>
              <Text size="xl" fw={700}>{analytics.upcomingDeadlines.length}</Text>
              <Text size="xs" c="dimmed">
                <Text component="span" c="red" fw={500}>
                  {analytics.upcomingDeadlines.filter(d => d.daysUntilDue <= 7).length} urgent
                </Text>
                {' • '}
                {analytics.upcomingDeadlines.filter(d => d.daysUntilDue <= 30).length} this month
              </Text>
            </Card>
          </Grid.Col>
        </Grid>

        {/* Quick Actions and Search */}
        <Card withBorder padding="lg" radius="md">
          <Group justify="space-between" align="center" mb="md">
            <Text fw={600} size="lg">Quick Actions & Filters</Text>
            <Badge variant="light" color="blue">
              L&D Operations
            </Badge>
          </Group>
          
          <Group gap="md" align="center">
            <TextInput
              placeholder="Search vendors, projects, invoices..."
              leftSection={<IconSearch size={16} />}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.currentTarget.value)}
              style={{ flex: 1, maxWidth: 300 }}
            />
            
            <Select
              placeholder="Filter by status"
              data={[
                { value: 'all', label: 'All Items' },
                { value: 'active', label: 'Active Only' },
                { value: 'pending', label: 'Pending Review' },
                { value: 'urgent', label: 'Urgent' },
              ]}
              value={selectedFilter}
              onChange={setSelectedFilter}
              leftSection={<IconFilter size={16} />}
            />

            <Button.Group>
              <Button variant="light" size="sm" leftSection={<IconFileText size={14} />}>
                Export Report
              </Button>
              <Button variant="light" size="sm" leftSection={<IconTrendingUp size={14} />}>
                Analytics
              </Button>
            </Button.Group>
          </Group>
        </Card>

        {/* Main Content Tabs */}
        <Tabs value={activeTab} onChange={(value) => setActiveTab(value || 'dashboard')} keepMounted={false}>
          <Tabs.List mb="md">
            <Tabs.Tab value="dashboard" leftSection={<IconChartBar size={16} />}>
              Dashboard
            </Tabs.Tab>
            <Tabs.Tab value="vendors" leftSection={<IconBuilding size={16} />}>
              Vendors
            </Tabs.Tab>
            <Tabs.Tab value="projects" leftSection={<IconUsers size={16} />}>
              Projects
            </Tabs.Tab>
            <Tabs.Tab value="proposals" leftSection={<IconFileText size={16} />}>
              Proposals
            </Tabs.Tab>
            <Tabs.Tab value="invoices" leftSection={<IconCurrencyDollar size={16} />}>
              Invoices
            </Tabs.Tab>
            <Tabs.Tab value="communications" leftSection={<IconMessage size={16} />}>
              Communications
            </Tabs.Tab>
          </Tabs.List>

          <Tabs.Panel value="dashboard">
            <VendorAnalyticsDashboard 
              analytics={analytics} 
              loading={loading}
              onRefresh={loadDashboardData}
            />
          </Tabs.Panel>

          <Tabs.Panel value="vendors">
            <VendorList 
              searchQuery={searchQuery}
              statusFilter={selectedFilter}
            />
          </Tabs.Panel>

          <Tabs.Panel value="projects">
            <ProjectList 
              searchQuery={searchQuery}
              statusFilter={selectedFilter}
            />
          </Tabs.Panel>

          <Tabs.Panel value="proposals">
            <ProposalList 
              searchQuery={searchQuery}
              statusFilter={selectedFilter}
            />
          </Tabs.Panel>

          <Tabs.Panel value="invoices">
            <InvoiceList 
              searchQuery={searchQuery}
              statusFilter={selectedFilter}
            />
          </Tabs.Panel>

          <Tabs.Panel value="communications">
            <CommunicationList 
              searchQuery={searchQuery}
              statusFilter={selectedFilter}
            />
          </Tabs.Panel>
        </Tabs>
      </Stack>

      {/* Create Vendor Modal */}
      <CreateVendorModal
        opened={createVendorModalOpen}
        onClose={() => setCreateVendorModalOpen(false)}
        onVendorCreated={handleVendorCreated}
      />
    </Container>
  );
};
