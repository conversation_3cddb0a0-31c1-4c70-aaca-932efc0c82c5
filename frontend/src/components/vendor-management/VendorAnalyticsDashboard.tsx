import React from 'react';
import { Card, Group, Text, Stack, Grid, Progress, Badge, ActionIcon, Tooltip, RingProgress, Button } from '@mantine/core';
import { 
  IconTrendingUp, 
  IconTrendingDown, 
  IconCalendar, 
  IconAlertTriangle, 
  IconCheck, 
  IconClock, 
  IconCurrencyDollar, 
  IconUsers, 
  IconChartBar,
  IconRefresh,
  IconFileText,
  IconBuilding
} from '@tabler/icons-react';
import { VendorAnalytics } from '../../types/vendor-management';

interface VendorAnalyticsDashboardProps {
  analytics: VendorAnalytics;
  loading?: boolean;
  onRefresh?: () => void;
}

export const VendorAnalyticsDashboard: React.FC<VendorAnalyticsDashboardProps> = ({ 
  analytics, 
  loading = false, 
  onRefresh 
}) => {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      notation: 'compact',
      maximumFractionDigits: 1
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric'
    });
  };

  const getDeadlineColor = (daysUntilDue: number) => {
    if (daysUntilDue <= 3) return 'red';
    if (daysUntilDue <= 7) return 'orange';
    if (daysUntilDue <= 30) return 'yellow';
    return 'green';
  };

  const getDeadlineIcon = (type: string) => {
    switch (type) {
      case 'contract_expiry': return <IconBuilding size={16} />;
      case 'project_deadline': return <IconUsers size={16} />;
      case 'invoice_due': return <IconCurrencyDollar size={16} />;
      case 'proposal_review': return <IconFileText size={16} />;
      default: return <IconCalendar size={16} />;
    }
  };

  const getDeadlineTitle = (type: string) => {
    switch (type) {
      case 'contract_expiry': return 'Contract Expiry';
      case 'project_deadline': return 'Project Deadline';
      case 'invoice_due': return 'Invoice Due';
      case 'proposal_review': return 'Proposal Review';
      default: return 'Deadline';
    }
  };

  return (
    <Stack gap="md">
      {/* Header with Refresh */}
      <Group justify="space-between" align="center">
        <Text fw={600} size="xl">Analytics Dashboard</Text>
        <Group gap="sm">
          <Text size="sm" c="dimmed">
            Last updated: {new Date().toLocaleTimeString()}
          </Text>
          <ActionIcon 
            variant="light" 
            onClick={onRefresh}
            loading={loading}
          >
            <IconRefresh size={16} />
          </ActionIcon>
        </Group>
      </Group>

      {/* Performance Overview */}
      <Grid>
        <Grid.Col span={{ base: 12, md: 4 }}>
          <Card withBorder padding="lg" radius="md" h="100%">
            <Group justify="space-between" align="flex-start" mb="md">
              <Text fw={600} size="lg">Performance Trends</Text>
              <IconTrendingUp size={20} color="#4CAF50" />
            </Group>
            
            <Stack gap="md">
              {analytics.performanceTrends.slice(-3).map((trend, index) => (
                <div key={trend.period}>
                  <Group justify="space-between" mb="xs">
                    <Text size="sm" fw={500}>{trend.period}</Text>
                    <Badge variant="light" color="blue" size="sm">
                      {trend.qualityScore.toFixed(1)}
                    </Badge>
                  </Group>
                  <Stack gap="xs">
                    <Group justify="space-between">
                      <Text size="xs" c="dimmed">On-time Delivery</Text>
                      <Text size="xs" fw={500}>{trend.onTimeDelivery}%</Text>
                    </Group>
                    <Progress value={trend.onTimeDelivery} color="green" size="sm" />
                    
                    <Group justify="space-between">
                      <Text size="xs" c="dimmed">Budget Adherence</Text>
                      <Text size="xs" fw={500}>{trend.budgetAdherence}%</Text>
                    </Group>
                    <Progress value={trend.budgetAdherence} color="blue" size="sm" />
                  </Stack>
                  {index < analytics.performanceTrends.length - 1 && <div style={{ height: 1, backgroundColor: '#e9ecef', margin: '8px 0' }} />}
                </div>
              ))}
            </Stack>
          </Card>
        </Grid.Col>

        <Grid.Col span={{ base: 12, md: 4 }}>
          <Card withBorder padding="lg" radius="md" h="100%">
            <Group justify="space-between" align="flex-start" mb="md">
              <Text fw={600} size="lg">Spending Breakdown</Text>
              <IconChartBar size={20} color="#FF9800" />
            </Group>
            
            <Stack gap="md">
              {analytics.spendingByCategory.slice(0, 5).map((category) => (
                <div key={category.category}>
                  <Group justify="space-between" mb="xs">
                    <Text size="sm" fw={500}>{category.category}</Text>
                    <Text size="sm" fw={600}>{formatCurrency(category.amount)}</Text>
                  </Group>
                  <Group justify="space-between" mb="xs">
                    <Text size="xs" c="dimmed">{category.percentage}% of total</Text>
                  </Group>
                  <Progress value={category.percentage} color="orange" size="sm" />
                </div>
              ))}
            </Stack>
          </Card>
        </Grid.Col>

        <Grid.Col span={{ base: 12, md: 4 }}>
          <Card withBorder padding="lg" radius="md" h="100%">
            <Group justify="space-between" align="flex-start" mb="md">
              <Text fw={600} size="lg">Vendor Health</Text>
              <IconCheck size={20} color="#4CAF50" />
            </Group>
            
            <Stack gap="lg" align="center">
              <RingProgress
                size={120}
                thickness={12}
                sections={[
                  { value: (analytics.activeVendors / analytics.totalVendors) * 100, color: 'green' },
                ]}
                label={
                  <Text c="green" fw={700} ta="center" size="lg">
                    {Math.round((analytics.activeVendors / analytics.totalVendors) * 100)}%
                  </Text>
                }
              />
              <Stack gap="xs" align="center">
                <Text fw={600} size="sm">Active Vendors</Text>
                <Text c="dimmed" size="xs">
                  {analytics.activeVendors} of {analytics.totalVendors} vendors are active
                </Text>
              </Stack>
            </Stack>
          </Card>
        </Grid.Col>
      </Grid>

      {/* Upcoming Deadlines */}
      <Card withBorder padding="lg" radius="md">
        <Group justify="space-between" align="center" mb="md">
          <Text fw={600} size="lg">
            <Group gap="sm">
              <IconAlertTriangle size={20} />
              Upcoming Deadlines
            </Group>
          </Text>
          <Badge variant="light" color="red">
            {analytics.upcomingDeadlines.filter(d => d.daysUntilDue <= 7).length} urgent
          </Badge>
        </Group>

        {analytics.upcomingDeadlines.length === 0 ? (
          <Group justify="center" py="xl">
            <Stack align="center" gap="xs">
              <IconCheck size={48} color="#4CAF50" />
              <Text fw={600} c="green">All caught up!</Text>
              <Text size="sm" c="dimmed">No upcoming deadlines to worry about</Text>
            </Stack>
          </Group>
        ) : (
          <Stack gap="md">
            {analytics.upcomingDeadlines.slice(0, 5).map((deadline, index) => (
              <Group key={index} justify="space-between" align="center" p="sm" style={{ 
                backgroundColor: deadline.daysUntilDue <= 7 ? '#fff3cd' : 'transparent',
                borderRadius: 4,
                border: deadline.daysUntilDue <= 7 ? '1px solid #ffeaa7' : '1px solid #e9ecef'
              }}>
                <Group gap="md" align="center">
                  <div style={{ color: getDeadlineColor(deadline.daysUntilDue) === 'red' ? '#dc3545' : 
                                       getDeadlineColor(deadline.daysUntilDue) === 'orange' ? '#fd7e14' : '#6c757d' }}>
                    {getDeadlineIcon(deadline.type)}
                  </div>
                  <Stack gap={2}>
                    <Text fw={500} size="sm">{getDeadlineTitle(deadline.type)}</Text>
                    <Text size="xs" c="dimmed">
                      {deadline.type === 'contract_expiry' && `${deadline.item.vendorName} contract`}
                      {deadline.type === 'project_deadline' && `${deadline.item.projectName} - ${deadline.item.vendorName}`}
                      {deadline.type === 'invoice_due' && `${deadline.item.invoiceNumber} - ${deadline.item.vendorName} (${formatCurrency(deadline.item.amount)})`}
                      {deadline.type === 'proposal_review' && `${deadline.item.title} - ${deadline.item.vendorName}`}
                    </Text>
                  </Stack>
                </Group>

                <Group gap="md" align="center">
                  <Stack gap={2} align="end">
                    <Text size="sm" fw={500}>
                      {formatDate(deadline.dueDate)}
                    </Text>
                    <Badge 
                      variant="filled" 
                      color={getDeadlineColor(deadline.daysUntilDue)}
                      size="sm"
                    >
                      {deadline.daysUntilDue === 0 ? 'Today' : 
                       deadline.daysUntilDue === 1 ? 'Tomorrow' : 
                       deadline.daysUntilDue < 0 ? `${Math.abs(deadline.daysUntilDue)} days overdue` :
                       `${deadline.daysUntilDue} days`}
                    </Badge>
                  </Stack>
                </Group>
              </Group>
            ))}
            
            {analytics.upcomingDeadlines.length > 5 && (
              <Group justify="center" mt="sm">
                <Button variant="light" size="sm">
                  View All Deadlines ({analytics.upcomingDeadlines.length - 5} more)
                </Button>
              </Group>
            )}
          </Stack>
        )}
      </Card>

      {/* Key Metrics Summary */}
      <Grid>
        <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
          <Card withBorder padding="md" radius="md">
            <Stack gap="xs" align="center">
              <Group gap="xs">
                <IconUsers size={16} color="#4CAF50" />
                <Text size="sm" c="dimmed">Avg Delivery Time</Text>
              </Group>
              <Text size="xl" fw={700}>
                {Math.round(analytics.performanceTrends.slice(-1)[0]?.onTimeDelivery || 0)}%
              </Text>
              <Text size="xs" c="green">On-time delivery rate</Text>
            </Stack>
          </Card>
        </Grid.Col>

        <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
          <Card withBorder padding="md" radius="md">
            <Stack gap="xs" align="center">
              <Group gap="xs">
                <IconCurrencyDollar size={16} color="#FF9800" />
                <Text size="sm" c="dimmed">Budget Performance</Text>
              </Group>
              <Text size="xl" fw={700}>
                {Math.round(analytics.performanceTrends.slice(-1)[0]?.budgetAdherence || 0)}%
              </Text>
              <Text size="xs" c="orange">Budget adherence</Text>
            </Stack>
          </Card>
        </Grid.Col>

        <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
          <Card withBorder padding="md" radius="md">
            <Stack gap="xs" align="center">
              <Group gap="xs">
                <IconChartBar size={16} color="#2196F3" />
                <Text size="sm" c="dimmed">Quality Score</Text>
              </Group>
              <Text size="xl" fw={700}>
                {(analytics.performanceTrends.slice(-1)[0]?.qualityScore || 0).toFixed(1)}
              </Text>
              <Text size="xs" c="blue">Average rating</Text>
            </Stack>
          </Card>
        </Grid.Col>

        <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
          <Card withBorder padding="md" radius="md">
            <Stack gap="xs" align="center">
              <Group gap="xs">
                <IconBuilding size={16} color="#9C27B0" />
                <Text size="sm" c="dimmed">Active Projects</Text>
              </Group>
              <Text size="xl" fw={700}>{analytics.activeProjects}</Text>
              <Text size="xs" c="violet">Currently running</Text>
            </Stack>
          </Card>
        </Grid.Col>
      </Grid>
    </Stack>
  );
};
