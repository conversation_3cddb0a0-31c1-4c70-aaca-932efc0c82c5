import React, { useState, useEffect } from 'react';
import { Card, Badge, Group, Text, Button, Stack, ActionIcon, Avatar, Menu, TextInput, Select, Pagination, Table, Tooltip, Progress, Loader } from '@mantine/core';
import { IconSearch, IconFilter, IconEye, IconEdit, IconTrash, IconDots, IconPhone, IconMail, IconGlobe, IconMapPin, IconStar, IconCalendar, IconBuilding, IconUsers, IconCurrencyDollar } from '@tabler/icons-react';
import { Vendor, VendorType, VendorStatus } from '../../types/vendor-management';
import { VendorDetailsModal } from './VendorDetailsModal';
import { EditVendorModal } from './EditVendorModal';
import { notifications } from '@mantine/notifications';

interface VendorListProps {
  searchQuery?: string;
  statusFilter?: string | null;
}

// Mock data for demonstration
const mockVendors: Vendor[] = [
  {
    id: '1',
    name: 'TechCorp Solutions',
    type: 'technology',
    status: 'active',
    contactPerson: '<PERSON>',
    email: '<EMAIL>',
    phone: '******-0123',
    website: 'https://techcorp.com',
    address: '123 Tech Street',
    city: 'San Francisco',
    country: 'USA',
    taxId: 'US123456789',
    rating: 4.8,
    totalProjects: 15,
    totalSpent: 450000,
    averageDeliveryTime: 28,
    specializations: ['Learning Management Systems', 'Mobile Learning', 'AI/ML Training'],
    certifications: ['ISO 27001', 'SOC 2', 'WCAG 2.1'],
    languages: ['English', 'Spanish'],
    timezone: 'PST',
    notes: 'Excellent track record with enterprise implementations',
    createdAt: '2023-01-15T00:00:00Z',
    updatedAt: '2024-06-01T00:00:00Z',
    lastContactDate: '2024-06-15T00:00:00Z',
    contractExpiryDate: '2024-12-31T00:00:00Z'
  },
  {
    id: '2',
    name: 'SkillBuilder Academy',
    type: 'training_provider',
    status: 'active',
    contactPerson: 'Michael Chen',
    email: '<EMAIL>',
    phone: '******-0456',
    website: 'https://skillbuilder.com',
    address: '456 Learning Avenue',
    city: 'New York',
    country: 'USA',
    rating: 4.6,
    totalProjects: 22,
    totalSpent: 680000,
    averageDeliveryTime: 35,
    specializations: ['Leadership Development', 'Soft Skills', 'Executive Coaching'],
    certifications: ['ICF Certified', 'ATD Member'],
    languages: ['English', 'French', 'Mandarin'],
    timezone: 'EST',
    notes: 'Strong in executive training programs',
    createdAt: '2023-03-20T00:00:00Z',
    updatedAt: '2024-06-10T00:00:00Z',
    lastContactDate: '2024-06-18T00:00:00Z',
    contractExpiryDate: '2025-03-20T00:00:00Z'
  },
  {
    id: '3',
    name: 'ComplianceFirst Ltd',
    type: 'consultant',
    status: 'pending',
    contactPerson: 'Dr. Emily Rodriguez',
    email: '<EMAIL>',
    phone: '******-0789',
    website: 'https://compliancefirst.com',
    address: '789 Compliance Road',
    city: 'Chicago',
    country: 'USA',
    rating: 4.9,
    totalProjects: 8,
    totalSpent: 320000,
    averageDeliveryTime: 21,
    specializations: ['Regulatory Training', 'Risk Management', 'Audit Preparation'],
    certifications: ['CISA', 'CISM', 'CRISC'],
    languages: ['English', 'Spanish'],
    timezone: 'CST',
    notes: 'Excellent for regulatory compliance training',
    createdAt: '2023-08-10T00:00:00Z',
    updatedAt: '2024-06-12T00:00:00Z',
    lastContactDate: '2024-06-16T00:00:00Z'
  }
];

export const VendorList: React.FC<VendorListProps> = ({ searchQuery = '', statusFilter = 'all' }) => {
  const [vendors, setVendors] = useState<Vendor[]>(mockVendors);
  const [filteredVendors, setFilteredVendors] = useState<Vendor[]>(mockVendors);
  const [loading, setLoading] = useState(false);
  const [selectedVendor, setSelectedVendor] = useState<Vendor | null>(null);
  const [detailsModalOpen, setDetailsModalOpen] = useState(false);
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(10);
  const [sortBy, setSortBy] = useState<string>('name');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  const [typeFilter, setTypeFilter] = useState<VendorType | 'all'>('all');

  useEffect(() => {
    filterVendors();
  }, [searchQuery, statusFilter, typeFilter, sortBy, sortOrder, vendors]);

  const filterVendors = () => {
    let filtered = [...vendors];

    // Apply search filter
    if (searchQuery) {
      filtered = filtered.filter(vendor => 
        vendor.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        vendor.contactPerson.toLowerCase().includes(searchQuery.toLowerCase()) ||
        vendor.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
        vendor.specializations.some(spec => spec.toLowerCase().includes(searchQuery.toLowerCase()))
      );
    }

    // Apply status filter
    if (statusFilter && statusFilter !== 'all') {
      if (statusFilter === 'urgent') {
        // Show vendors with contracts expiring soon or requiring attention
        const urgentDate = new Date();
        urgentDate.setMonth(urgentDate.getMonth() + 1); // Next 30 days
        filtered = filtered.filter(vendor => 
          (vendor.contractExpiryDate && new Date(vendor.contractExpiryDate) <= urgentDate) ||
          vendor.status === 'pending'
        );
      } else {
        filtered = filtered.filter(vendor => vendor.status === statusFilter);
      }
    }

    // Apply type filter
    if (typeFilter !== 'all') {
      filtered = filtered.filter(vendor => vendor.type === typeFilter);
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue: any = a[sortBy as keyof Vendor];
      let bValue: any = b[sortBy as keyof Vendor];

      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }

      if (sortOrder === 'asc') {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      }
    });

    setFilteredVendors(filtered);
    setCurrentPage(1);
  };

  const handleViewDetails = (vendor: Vendor) => {
    setSelectedVendor(vendor);
    setDetailsModalOpen(true);
  };

  const handleEditVendor = (vendor: Vendor) => {
    setSelectedVendor(vendor);
    setEditModalOpen(true);
  };

  const handleDeleteVendor = async (vendor: Vendor) => {
    if (window.confirm(`Are you sure you want to delete ${vendor.name}?`)) {
      try {
        // TODO: Replace with actual API call
        // await vendorService.deleteVendor(vendor.id);
        
        setVendors(prev => prev.filter(v => v.id !== vendor.id));
        notifications.show({
          title: 'Success',
          message: 'Vendor deleted successfully',
          color: 'green'
        });
      } catch (error) {
        notifications.show({
          title: 'Error',
          message: 'Failed to delete vendor',
          color: 'red'
        });
      }
    }
  };

  const handleVendorUpdated = (updatedVendor: Vendor) => {
    setVendors(prev => prev.map(v => v.id === updatedVendor.id ? updatedVendor : v));
    setEditModalOpen(false);
    notifications.show({
      title: 'Success',
      message: 'Vendor updated successfully',
      color: 'green'
    });
  };

  const getStatusColor = (status: VendorStatus) => {
    switch (status) {
      case 'active': return 'green';
      case 'pending': return 'yellow';
      case 'suspended': return 'red';
      case 'inactive': return 'gray';
      default: return 'blue';
    }
  };

  const getTypeLabel = (type: VendorType) => {
    switch (type) {
      case 'training_provider': return 'Training Provider';
      case 'consultant': return 'Consultant';
      case 'technology': return 'Technology';
      case 'content_creator': return 'Content Creator';
      case 'assessment': return 'Assessment';
      case 'platform': return 'Platform';
      default: return type;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      notation: 'compact',
      maximumFractionDigits: 1
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const paginatedVendors = filteredVendors.slice(
    (currentPage - 1) * pageSize,
    currentPage * pageSize
  );

  return (
    <Stack gap="md">
      {/* Filters and Controls */}
      <Card withBorder padding="md" radius="md">
        <Group justify="space-between" align="center" mb="md">
          <Text fw={600} size="lg">Vendor Directory</Text>
          <Badge variant="light" color="blue">
            {filteredVendors.length} vendors
          </Badge>
        </Group>

        <Group gap="md" align="center">
          <Select
            placeholder="Filter by type"
            data={[
              { value: 'all', label: 'All Types' },
              { value: 'training_provider', label: 'Training Provider' },
              { value: 'consultant', label: 'Consultant' },
              { value: 'technology', label: 'Technology' },
              { value: 'content_creator', label: 'Content Creator' },
              { value: 'assessment', label: 'Assessment' },
              { value: 'platform', label: 'Platform' }
            ]}
            value={typeFilter}
            onChange={(value) => setTypeFilter(value as VendorType | 'all')}
            leftSection={<IconFilter size={16} />}
            style={{ minWidth: 200 }}
          />

          <Select
            placeholder="Sort by"
            data={[
              { value: 'name', label: 'Name' },
              { value: 'rating', label: 'Rating' },
              { value: 'totalSpent', label: 'Total Spent' },
              { value: 'totalProjects', label: 'Projects' },
              { value: 'createdAt', label: 'Date Added' }
            ]}
            value={sortBy}
            onChange={(value) => setSortBy(value || 'name')}
            style={{ minWidth: 150 }}
          />

          <Button
            variant={sortOrder === 'asc' ? 'filled' : 'light'}
            size="sm"
            onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
          >
            {sortOrder === 'asc' ? 'A-Z' : 'Z-A'}
          </Button>
        </Group>
      </Card>

      {/* Vendor Cards */}
      {loading ? (
        <Card withBorder padding="xl" radius="md">
          <Group justify="center">
            <Loader size="md" />
            <Text>Loading vendors...</Text>
          </Group>
        </Card>
      ) : paginatedVendors.length === 0 ? (
        <Card withBorder padding="xl" radius="md">
          <Group justify="center">
            <IconBuilding size={48} color="#ccc" />
            <Stack gap="xs" align="center">
              <Text size="lg" fw={600} c="dimmed">No vendors found</Text>
              <Text size="sm" c="dimmed">Try adjusting your filters or search criteria</Text>
            </Stack>
          </Group>
        </Card>
      ) : (
        <Stack gap="md">
          {paginatedVendors.map((vendor) => (
            <Card key={vendor.id} withBorder padding="lg" radius="md" className="hover:shadow-md transition-shadow">
              <Group justify="space-between" align="flex-start">
                <Group align="flex-start" gap="md">
                  <Avatar size="lg" radius="md" color="blue">
                    <IconBuilding size={24} />
                  </Avatar>
                  
                  <Stack gap="xs" style={{ flex: 1 }}>
                    <Group gap="sm" align="center">
                      <Text fw={700} size="lg">{vendor.name}</Text>
                      <Badge 
                        variant="light" 
                        color={getStatusColor(vendor.status)}
                        size="sm"
                      >
                        {vendor.status.charAt(0).toUpperCase() + vendor.status.slice(1)}
                      </Badge>
                      <Badge variant="outline" size="sm">
                        {getTypeLabel(vendor.type)}
                      </Badge>
                    </Group>

                    <Group gap="md" c="dimmed" fz="sm">
                      <Group gap={4}>
                        <IconUsers size={14} />
                        <Text>{vendor.contactPerson}</Text>
                      </Group>
                      <Group gap={4}>
                        <IconMail size={14} />
                        <Text>{vendor.email}</Text>
                      </Group>
                      <Group gap={4}>
                        <IconMapPin size={14} />
                        <Text>{vendor.city}, {vendor.country}</Text>
                      </Group>
                    </Group>

                    <Group gap="md" align="center">
                      <Group gap={4}>
                        <IconStar size={14} fill="currentColor" color="#FFD700" />
                        <Text fw={600}>{vendor.rating}</Text>
                      </Group>
                      <Group gap={4}>
                        <IconCurrencyDollar size={14} />
                        <Text>{formatCurrency(vendor.totalSpent)}</Text>
                      </Group>
                      <Group gap={4}>
                        <IconBuilding size={14} />
                        <Text>{vendor.totalProjects} projects</Text>
                      </Group>
                      <Group gap={4}>
                        <IconCalendar size={14} />
                        <Text>Avg {vendor.averageDeliveryTime} days</Text>
                      </Group>
                    </Group>

                    <Group gap="xs">
                      {vendor.specializations.slice(0, 3).map((spec, index) => (
                        <Badge key={index} variant="dot" size="sm">
                          {spec}
                        </Badge>
                      ))}
                      {vendor.specializations.length > 3 && (
                        <Badge variant="light" size="sm" c="dimmed">
                          +{vendor.specializations.length - 3} more
                        </Badge>
                      )}
                    </Group>
                  </Stack>
                </Group>

                <Group gap="xs">
                  <Tooltip label="View Details">
                    <ActionIcon 
                      variant="light" 
                      size="lg"
                      onClick={() => handleViewDetails(vendor)}
                    >
                      <IconEye size={16} />
                    </ActionIcon>
                  </Tooltip>

                  <Tooltip label="Edit Vendor">
                    <ActionIcon 
                      variant="light" 
                      size="lg"
                      onClick={() => handleEditVendor(vendor)}
                    >
                      <IconEdit size={16} />
                    </ActionIcon>
                  </Tooltip>

                  <Menu shadow="md" width={200}>
                    <Menu.Target>
                      <ActionIcon variant="light" size="lg">
                        <IconDots size={16} />
                      </ActionIcon>
                    </Menu.Target>

                    <Menu.Dropdown>
                      <Menu.Item leftSection={<IconPhone size={14} />}>
                        Call Vendor
                      </Menu.Item>
                      <Menu.Item leftSection={<IconMail size={14} />}>
                        Send Email
                      </Menu.Item>
                      <Menu.Item leftSection={<IconGlobe size={14} />}>
                        Visit Website
                      </Menu.Item>
                      <Menu.Divider />
                      <Menu.Item 
                        leftSection={<IconTrash size={14} />} 
                        color="red"
                        onClick={() => handleDeleteVendor(vendor)}
                      >
                        Delete Vendor
                      </Menu.Item>
                    </Menu.Dropdown>
                  </Menu>
                </Group>
              </Group>

              {/* Contract Expiry Warning */}
              {vendor.contractExpiryDate && (() => {
                const expiryDate = new Date(vendor.contractExpiryDate);
                const today = new Date();
                const daysUntilExpiry = Math.ceil((expiryDate.getTime() - today.getTime()) / (1000 * 3600 * 24));
                
                if (daysUntilExpiry <= 30 && daysUntilExpiry > 0) {
                  return (
                    <Group gap="xs" mt="sm" p="xs" style={{ backgroundColor: '#fff3cd', borderRadius: 4 }}>
                      <IconCalendar size={14} color="#856404" />
                      <Text size="sm" c="#856404">
                        Contract expires in {daysUntilExpiry} days ({formatDate(vendor.contractExpiryDate)})
                      </Text>
                    </Group>
                  );
                } else if (daysUntilExpiry <= 0) {
                  return (
                    <Group gap="xs" mt="sm" p="xs" style={{ backgroundColor: '#f8d7da', borderRadius: 4 }}>
                      <IconCalendar size={14} color="#721c24" />
                      <Text size="sm" c="#721c24">
                        Contract expired {Math.abs(daysUntilExpiry)} days ago
                      </Text>
                    </Group>
                  );
                }
                return null;
              })()}
            </Card>
          ))}
        </Stack>
      )}

      {/* Pagination */}
      {filteredVendors.length > pageSize && (
        <Group justify="center" mt="md">
          <Pagination
            value={currentPage}
            onChange={setCurrentPage}
            total={Math.ceil(filteredVendors.length / pageSize)}
            size="sm"
          />
        </Group>
      )}

      {/* Modals */}
      {selectedVendor && (
        <>
          <VendorDetailsModal
            vendor={selectedVendor}
            opened={detailsModalOpen}
            onClose={() => setDetailsModalOpen(false)}
          />
          <EditVendorModal
            vendor={selectedVendor}
            opened={editModalOpen}
            onClose={() => setEditModalOpen(false)}
            onVendorUpdated={handleVendorUpdated}
          />
        </>
      )}
    </Stack>
  );
};
