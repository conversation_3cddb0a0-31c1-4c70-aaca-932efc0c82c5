/**
 * Export Service for Training Needs Analysis
 * Handles PDF generation, data export, and sharing functionality
 */

import { TrainingNeedsAnalysisData } from '../../../types/training-needs-analysis';

export interface ExportOptions {
  format: 'pdf' | 'json' | 'csv';
  includePersonalInfo: boolean;
  includeSkillsDetails: boolean;
  includeApprovalHistory: boolean;
  template: 'detailed' | 'summary' | 'executive';
}

export interface ShareOptions {
  recipients: string[];
  subject?: string;
  message?: string;
  includeAttachment: boolean;
}

class ExportService {
  /**
   * Generate PDF report of training needs analysis
   */
  static async generatePDF(
    data: TrainingNeedsAnalysisData, 
    options: ExportOptions = {
      format: 'pdf',
      includePersonalInfo: true,
      includeSkillsDetails: true,
      includeApprovalHistory: true,
      template: 'detailed'
    }
  ): Promise<Blob> {
    // In a real implementation, this would use a PDF library like jsPDF or Puppeteer
    // For now, we'll create a mock implementation
    
    const content = this.generateHTMLContent(data, options);
    
    // Mock PDF generation - in real app would use proper PDF library
    const htmlBlob = new Blob([content], { type: 'text/html' });
    
    // Simulate async PDF generation
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    return htmlBlob;
  }

  /**
   * Export data as JSON
   */
  static exportJSON(data: TrainingNeedsAnalysisData): Blob {
    const jsonData = {
      ...data,
      exportedAt: new Date().toISOString(),
      exportedBy: data.employeeInfo?.name || 'Unknown'
    };
    
    const content = JSON.stringify(jsonData, null, 2);
    return new Blob([content], { type: 'application/json' });
  }

  /**
   * Export data as CSV
   */
  static exportCSV(data: TrainingNeedsAnalysisData): Blob {
    const rows: string[] = [];
    
    // Headers
    rows.push('Category,Field,Value');
    
    // Employee Information
    if (data.employeeInfo) {
      rows.push(`Employee Info,Name,"${data.employeeInfo.name || ''}"`);
      rows.push(`Employee Info,Department,"${data.employeeInfo.department || ''}"`);
      rows.push(`Employee Info,Role,"${data.employeeInfo.role || ''}"`);
      rows.push(`Employee Info,Manager,"${data.employeeInfo.manager || ''}"`);
    }
    
    // Skills Assessment
    if (data.skillsAssessment) {
      const allSkills = [
        ...(data.skillsAssessment.technicalSkills || []),
        ...(data.skillsAssessment.softSkills || []),
        ...(data.skillsAssessment.leadershipSkills || []),
        ...(data.skillsAssessment.complianceSkills || [])
      ];
      
      allSkills.forEach(skill => {
        rows.push(`Skills,${skill.skillName},"Current: ${skill.currentLevel}, Target: ${skill.targetLevel}, Importance: ${skill.importance}"`);
      });
    }
    
    // Performance Gaps
    if (data.performanceGaps) {
      data.performanceGaps.forEach((gap, index) => {
        rows.push(`Performance Gap ${index + 1},Area,"${gap.gapArea || ''}"`);
        rows.push(`Performance Gap ${index + 1},Severity,"${gap.severity || ''}"`);
        rows.push(`Performance Gap ${index + 1},Description,"${gap.description || ''}"`);
      });
    }
    
    // Budget and Timeline
    if (data.budgetTimeline) {
      rows.push(`Budget,Total Budget,"${data.budgetTimeline.totalBudget || 0}"`);
      rows.push(`Timeline,Preferred Timeline,"${data.budgetTimeline.preferredTimeline || ''}"`);
    }
    
    const content = rows.join('\n');
    return new Blob([content], { type: 'text/csv' });
  }

  /**
   * Generate HTML content for PDF
   */
  private static generateHTMLContent(data: TrainingNeedsAnalysisData, options: ExportOptions): string {
    const now = new Date();
    
    return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Training Needs Analysis Report</title>
    <style>
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            line-height: 1.6; 
            color: #333; 
            max-width: 800px; 
            margin: 0 auto; 
            padding: 20px; 
        }
        .header { 
            text-align: center; 
            border-bottom: 3px solid #2563eb; 
            padding-bottom: 20px; 
            margin-bottom: 30px; 
        }
        .header h1 { 
            color: #2563eb; 
            margin: 0; 
            font-size: 28px; 
        }
        .section { 
            margin-bottom: 30px; 
            padding: 20px; 
            border-left: 4px solid #e5e7eb; 
            background: #f9fafb; 
        }
        .section h2 { 
            color: #374151; 
            border-bottom: 2px solid #d1d5db; 
            padding-bottom: 10px; 
            margin-top: 0; 
        }
        .employee-info { 
            display: grid; 
            grid-template-columns: 1fr 1fr; 
            gap: 15px; 
        }
        .info-item { 
            padding: 10px; 
            background: white; 
            border-radius: 6px; 
            border: 1px solid #e5e7eb; 
        }
        .info-label { 
            font-weight: 600; 
            color: #6b7280; 
            font-size: 12px; 
            text-transform: uppercase; 
            letter-spacing: 0.5px; 
        }
        .info-value { 
            font-size: 16px; 
            color: #111827; 
            margin-top: 5px; 
        }
        .skills-category { 
            margin-bottom: 20px; 
        }
        .skills-category h3 { 
            color: #2563eb; 
            margin-bottom: 10px; 
        }
        .skill-item { 
            display: flex; 
            justify-content: space-between; 
            align-items: center; 
            padding: 8px 12px; 
            background: white; 
            border: 1px solid #e5e7eb; 
            border-radius: 6px; 
            margin-bottom: 5px; 
        }
        .skill-name { 
            font-weight: 500; 
        }
        .skill-levels { 
            font-size: 14px; 
            color: #6b7280; 
        }
        .gap-item { 
            background: white; 
            border: 1px solid #e5e7eb; 
            border-radius: 6px; 
            padding: 15px; 
            margin-bottom: 15px; 
        }
        .gap-severity { 
            display: inline-block; 
            padding: 4px 8px; 
            border-radius: 4px; 
            font-size: 12px; 
            font-weight: 600; 
            text-transform: uppercase; 
        }
        .severity-high { 
            background: #fef2f2; 
            color: #dc2626; 
            border: 1px solid #fecaca; 
        }
        .severity-medium { 
            background: #fffbeb; 
            color: #d97706; 
            border: 1px solid #fed7aa; 
        }
        .severity-low { 
            background: #f0fdf4; 
            color: #16a34a; 
            border: 1px solid #bbf7d0; 
        }
        .footer { 
            margin-top: 40px; 
            text-align: center; 
            color: #6b7280; 
            font-size: 14px; 
            border-top: 1px solid #e5e7eb; 
            padding-top: 20px; 
        }
        @media print {
            body { padding: 0; }
            .section { break-inside: avoid; }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Training Needs Analysis Report</h1>
        <p>Generated on ${now.toLocaleDateString()} at ${now.toLocaleTimeString()}</p>
    </div>

    ${options.includePersonalInfo && data.employeeInfo ? `
    <div class="section">
        <h2>Employee Information</h2>
        <div class="employee-info">
            <div class="info-item">
                <div class="info-label">Name</div>
                <div class="info-value">${data.employeeInfo.name || 'N/A'}</div>
            </div>
            <div class="info-item">
                <div class="info-label">Department</div>
                <div class="info-value">${data.employeeInfo.department || 'N/A'}</div>
            </div>
            <div class="info-item">
                <div class="info-label">Role</div>
                <div class="info-value">${data.employeeInfo.role || 'N/A'}</div>
            </div>
            <div class="info-item">
                <div class="info-label">Manager</div>
                <div class="info-value">${data.employeeInfo.manager || 'N/A'}</div>
            </div>
            <div class="info-item">
                <div class="info-label">Location</div>
                <div class="info-value">${data.employeeInfo.location || 'N/A'}</div>
            </div>
            <div class="info-item">
                <div class="info-label">Years in Role</div>
                <div class="info-value">${data.employeeInfo.yearsInRole || 0} years</div>
            </div>
        </div>
    </div>
    ` : ''}

    ${options.includeSkillsDetails && data.skillsAssessment ? `
    <div class="section">
        <h2>Skills Assessment</h2>
        <p><strong>Overall Competency Level:</strong> ${data.skillsAssessment.overallCompetencyLevel || 0}/5.0</p>
        
        ${data.skillsAssessment.technicalSkills && data.skillsAssessment.technicalSkills.length > 0 ? `
        <div class="skills-category">
            <h3>Technical Skills</h3>
            ${data.skillsAssessment.technicalSkills.map(skill => `
                <div class="skill-item">
                    <span class="skill-name">${skill.skillName}</span>
                    <span class="skill-levels">Current: ${skill.currentLevel}/5 → Target: ${skill.targetLevel}/5</span>
                </div>
            `).join('')}
        </div>
        ` : ''}
        
        ${data.skillsAssessment.softSkills && data.skillsAssessment.softSkills.length > 0 ? `
        <div class="skills-category">
            <h3>Soft Skills</h3>
            ${data.skillsAssessment.softSkills.map(skill => `
                <div class="skill-item">
                    <span class="skill-name">${skill.skillName}</span>
                    <span class="skill-levels">Current: ${skill.currentLevel}/5 → Target: ${skill.targetLevel}/5</span>
                </div>
            `).join('')}
        </div>
        ` : ''}
        
        ${data.skillsAssessment.leadershipSkills && data.skillsAssessment.leadershipSkills.length > 0 ? `
        <div class="skills-category">
            <h3>Leadership Skills</h3>
            ${data.skillsAssessment.leadershipSkills.map(skill => `
                <div class="skill-item">
                    <span class="skill-name">${skill.skillName}</span>
                    <span class="skill-levels">Current: ${skill.currentLevel}/5 → Target: ${skill.targetLevel}/5</span>
                </div>
            `).join('')}
        </div>
        ` : ''}
    </div>
    ` : ''}

    ${data.performanceGaps && data.performanceGaps.length > 0 ? `
    <div class="section">
        <h2>Performance Gaps</h2>
        ${data.performanceGaps.map(gap => `
            <div class="gap-item">
                <h4>${gap.gapArea || 'Performance Gap'}</h4>
                <span class="gap-severity severity-${gap.severity || 'medium'}">${gap.severity || 'Medium'} Severity</span>
                <p><strong>Description:</strong> ${gap.description || 'No description provided'}</p>
                <p><strong>Current Performance:</strong> ${gap.currentPerformance || 'N/A'}</p>
                <p><strong>Target Performance:</strong> ${gap.targetPerformance || 'N/A'}</p>
            </div>
        `).join('')}
    </div>
    ` : ''}

    ${data.budgetTimeline ? `
    <div class="section">
        <h2>Budget & Timeline</h2>
        <div class="employee-info">
            <div class="info-item">
                <div class="info-label">Total Budget</div>
                <div class="info-value">$${(data.budgetTimeline.totalBudget || 0).toLocaleString()}</div>
            </div>
            <div class="info-item">
                <div class="info-label">Preferred Timeline</div>
                <div class="info-value">${data.budgetTimeline.preferredTimeline || 'Not specified'}</div>
            </div>
        </div>
    </div>
    ` : ''}

    ${data.learningObjectives && data.learningObjectives.length > 0 ? `
    <div class="section">
        <h2>Learning Objectives</h2>
        ${data.learningObjectives.map((objective, index) => `
            <div class="gap-item">
                <h4>Objective ${index + 1}: ${objective.title || 'Untitled'}</h4>
                <p><strong>Description:</strong> ${objective.description || 'No description'}</p>
                <p><strong>Target Date:</strong> ${objective.targetDate ? new Date(objective.targetDate).toLocaleDateString() : 'Not set'}</p>
                <p><strong>Success Criteria:</strong> ${objective.successCriteria || 'Not defined'}</p>
            </div>
        `).join('')}
    </div>
    ` : ''}

    <div class="footer">
        <p>This report was generated by the Training Needs Analysis system.</p>
        <p>For questions or support, please contact your HR department.</p>
    </div>
</body>
</html>`;
  }

  /**
   * Download file with given content
   */
  static downloadFile(blob: Blob, filename: string): void {
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }

  /**
   * Share report via email (mock implementation)
   */
  static async shareViaEmail(
    data: TrainingNeedsAnalysisData,
    options: ShareOptions
  ): Promise<{ success: boolean; message: string }> {
    // Mock email sharing - in real app would integrate with email service
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    const subject = options.subject || `Training Needs Analysis - ${data.employeeInfo?.name || 'Employee'}`;
    const message = options.message || 'Please find the attached training needs analysis report.';
    
    // In real implementation, would call email API
    console.log('Email would be sent with:', {
      to: options.recipients,
      subject,
      message,
      hasAttachment: options.includeAttachment
    });
    
    return {
      success: true,
      message: `Report shared successfully with ${options.recipients.length} recipient(s)`
    };
  }

  /**
   * Generate filename based on data and format
   */
  static generateFilename(data: TrainingNeedsAnalysisData, format: string): string {
    const employeeName = data.employeeInfo?.name?.replace(/\s+/g, '_') || 'Employee';
    const date = new Date().toISOString().split('T')[0];
    const extension = format === 'pdf' ? 'html' : format; // Using HTML instead of PDF for demo
    
    return `Training_Needs_Analysis_${employeeName}_${date}.${extension}`;
  }
}

export default ExportService;