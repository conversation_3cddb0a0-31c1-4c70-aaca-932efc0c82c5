import React from 'react';
import { MantineProvider, Stack } from '@mantine/core';
import { Notifications } from '@mantine/notifications';
import { WizardContainer } from './components/wizard/WizardContainer';
import { useTrainingFormStore } from './stores/trainingFormStore';
import { EnhancedHeader } from './components/shared/EnhancedHeader';

interface TrainingNeedsAnalysisFormProps {
  onComplete?: () => void;
  onCancel?: () => void;
  theme?: 'light' | 'dark';
}

export const TrainingNeedsAnalysisForm: React.FC<TrainingNeedsAnalysisFormProps> = ({
  onComplete,
  onCancel,
  theme = 'light'
}) => {
  const resetForm = useTrainingFormStore((state) => state.resetForm);

  const handleCancel = () => {
    resetForm();
    onCancel?.();
  };

  return (
    <MantineProvider>
      <Notifications position="top-right" zIndex={1000} />
      <Stack gap="lg" p="md" bg="gray.0" mih="100vh">
        <EnhancedHeader
          title="Training Needs Analysis"
          subtitle="Comprehensive skill assessment and development planning"
          showExportOptions={true}
          showShareOptions={true}
        />
        <WizardContainer
          onComplete={onComplete}
          onCancel={handleCancel}
        />
      </Stack>
    </MantineProvider>
  );
};

export default TrainingNeedsAnalysisForm;