import React, { useState, useMemo } from 'react';
import {
  <PERSON>ack,
  Grid,
  Text,
  Card,
  Tabs,
  Group,
  Badge,
  Button,
  Box,
  Progress,
  Rating,
  Select,
  Textarea,
  ActionIcon,
  Modal,
  NumberInput,
  TextInput,
  Tooltip,
  Alert,
  Chip,
  RingProgress
} from '@mantine/core';
import { IconPlus, IconX, IconStar, IconBrain, IconUsers, IconTrophy, IconShield, IconSparkles, IconTrendingUp, IconTarget, IconLightbulb } from '@tabler/icons-react';
import { motion, AnimatePresence } from 'framer-motion';
import { useTrainingFormStore } from '../../stores/trainingFormStore';
import { SkillRating, SkillsAssessmentData } from '../../../../types/training-needs-analysis';
import { EnhancedValidation, ValidationRule } from '../shared/EnhancedValidation';
import SmartSuggestionsService from '../../services/smartSuggestions';

// Predefined skills by category
const predefinedSkills = {
  technical: [
    'JavaScript/TypeScript',
    'React/Angular/Vue',
    'Node.js',
    'Python',
    'SQL Databases',
    'Cloud Platforms (AWS/Azure/GCP)',
    'DevOps/CI/CD',
    'Data Analysis',
    'Machine Learning',
    'Cybersecurity',
    'Mobile Development',
    'UI/UX Design'
  ],
  soft: [
    'Communication',
    'Problem Solving',
    'Time Management',
    'Adaptability',
    'Creativity',
    'Critical Thinking',
    'Collaboration',
    'Emotional Intelligence',
    'Presentation Skills',
    'Conflict Resolution',
    'Active Listening',
    'Customer Service'
  ],
  leadership: [
    'Team Management',
    'Strategic Planning',
    'Decision Making',
    'Delegation',
    'Coaching & Mentoring',
    'Performance Management',
    'Change Management',
    'Vision Setting',
    'Stakeholder Management',
    'Budget Management',
    'Risk Management',
    'Talent Development'
  ],
  compliance: [
    'Data Privacy (GDPR)',
    'Financial Regulations',
    'Safety Protocols',
    'Quality Standards',
    'Ethics & Compliance',
    'Audit Procedures',
    'Regulatory Reporting',
    'Risk Assessment',
    'Policy Development',
    'Training Delivery',
    'Documentation',
    'Legal Requirements'
  ]
};

const skillCategories = [
  { value: 'technical', label: 'Technical Skills', icon: IconBrain, color: 'blue' },
  { value: 'soft', label: 'Soft Skills', icon: IconUsers, color: 'green' },
  { value: 'leadership', label: 'Leadership Skills', icon: IconTrophy, color: 'orange' },
  { value: 'compliance', label: 'Compliance Skills', icon: IconShield, color: 'red' }
];

export const SkillsAssessmentStep: React.FC = () => {
  const { formData, updateFormData } = useTrainingFormStore();
  const [activeTab, setActiveTab] = useState<string>('technical');
  const [addSkillModal, setAddSkillModal] = useState(false);
  const [newSkillName, setNewSkillName] = useState('');
  const [newSkillCategory, setNewSkillCategory] = useState<string>('technical');
  const [showSuggestions, setShowSuggestions] = useState(false);

  // Smart suggestions based on employee profile
  const smartSuggestions = useMemo(() => {
    if (formData.employeeInfo?.department && formData.employeeInfo?.role) {
      return SmartSuggestionsService.getSkillSuggestions(
        formData.employeeInfo.department,
        formData.employeeInfo.role
      );
    }
    return [];
  }, [formData.employeeInfo?.department, formData.employeeInfo?.role]);

  // Validation rules for skills assessment
  const validationRules: ValidationRule[] = [
    {
      field: 'technicalSkills',
      required: true,
      customValidation: (skills: SkillRating[]) => ({
        isValid: skills.length >= 2,
        message: skills.length === 0 ? 'Add at least 2 technical skills' : 
                 skills.length === 1 ? 'Add at least 1 more technical skill' : undefined
      }),
      qualityBonus: 20
    },
    {
      field: 'softSkills', 
      required: true,
      customValidation: (skills: SkillRating[]) => ({
        isValid: skills.length >= 2,
        message: skills.length === 0 ? 'Add at least 2 soft skills' : 
                 skills.length === 1 ? 'Add at least 1 more soft skill' : undefined
      }),
      qualityBonus: 20
    },
    {
      field: 'leadershipSkills',
      required: false,
      customValidation: (skills: SkillRating[]) => ({
        isValid: true,
        message: formData.employeeInfo?.directReports && formData.employeeInfo.directReports > 0 && skills.length === 0 ?
                 'Consider adding leadership skills as you have direct reports' : undefined
      }),
      qualityBonus: 15
    },
    {
      field: 'complianceSkills',
      required: false,
      qualityBonus: 10
    }
  ];

  const skillsData = formData.skillsAssessment || {
    technicalSkills: [],
    softSkills: [],
    leadershipSkills: [],
    complianceSkills: [],
    overallCompetencyLevel: 0,
    selfAssessmentComplete: false,
    managerAssessmentComplete: false
  };

  // Get skills for current category
  const getCurrentCategorySkills = (): SkillRating[] => {
    switch (activeTab) {
      case 'technical': return skillsData.technicalSkills || [];
      case 'soft': return skillsData.softSkills || [];
      case 'leadership': return skillsData.leadershipSkills || [];
      case 'compliance': return skillsData.complianceSkills || [];
      default: return [];
    }
  };

  // Update skills for current category
  const updateCategorySkills = (skills: SkillRating[]) => {
    const categoryKey = `${activeTab}Skills` as keyof SkillsAssessmentData;
    const updatedSkillsData = {
      ...skillsData,
      [categoryKey]: skills
    };
    
    // Calculate overall competency level
    const allSkills = [
      ...(updatedSkillsData.technicalSkills || []),
      ...(updatedSkillsData.softSkills || []),
      ...(updatedSkillsData.leadershipSkills || []),
      ...(updatedSkillsData.complianceSkills || [])
    ];
    
    const averageLevel = allSkills.length > 0 
      ? allSkills.reduce((sum, skill) => sum + skill.currentLevel, 0) / allSkills.length
      : 0;
    
    updatedSkillsData.overallCompetencyLevel = Math.round(averageLevel * 10) / 10;
    
    updateFormData('skillsAssessment', updatedSkillsData);
  };

  // Add a new skill
  const addSkill = (skillName: string, category: string) => {
    const newSkill: SkillRating = {
      id: crypto.randomUUID(),
      skillName,
      category: category as any,
      currentLevel: 1,
      targetLevel: 3,
      importance: 'medium',
      lastAssessed: new Date(),
      notes: ''
    };

    const categoryKey = `${category}Skills` as keyof SkillsAssessmentData;
    const currentSkills = skillsData[categoryKey] as SkillRating[] || [];
    
    // Check if skill already exists
    if (currentSkills.some(skill => skill.skillName.toLowerCase() === skillName.toLowerCase())) {
      return false; // Skill already exists
    }

    const updatedSkills = [...currentSkills, newSkill];
    const updatedSkillsData = {
      ...skillsData,
      [categoryKey]: updatedSkills
    };
    
    updateFormData('skillsAssessment', updatedSkillsData);
    return true;
  };

  // Update skill rating
  const updateSkillRating = (skillId: string, field: keyof SkillRating, value: any) => {
    const currentSkills = getCurrentCategorySkills();
    const updatedSkills = currentSkills.map(skill =>
      skill.id === skillId ? { ...skill, [field]: value } : skill
    );
    updateCategorySkills(updatedSkills);
  };

  // Remove skill
  const removeSkill = (skillId: string) => {
    const currentSkills = getCurrentCategorySkills();
    const updatedSkills = currentSkills.filter(skill => skill.id !== skillId);
    updateCategorySkills(updatedSkills);
  };

  // Add predefined skill
  const addPredefinedSkill = (skillName: string) => {
    if (addSkill(skillName, activeTab)) {
      // Skill added successfully
    }
  };

  // Add custom skill
  const handleAddCustomSkill = () => {
    if (newSkillName.trim() && addSkill(newSkillName.trim(), newSkillCategory)) {
      setNewSkillName('');
      setAddSkillModal(false);
    }
  };

  // Get skill level text
  const getSkillLevelText = (level: number): string => {
    switch (level) {
      case 1: return 'Beginner';
      case 2: return 'Basic';
      case 3: return 'Intermediate';
      case 4: return 'Advanced';
      case 5: return 'Expert';
      default: return 'Not Rated';
    }
  };

  // Get importance color
  const getImportanceColor = (importance: string): string => {
    switch (importance) {
      case 'low': return 'gray';
      case 'medium': return 'blue';
      case 'high': return 'orange';
      case 'critical': return 'red';
      default: return 'gray';
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <Stack gap="lg">
        <div>
          <Text size="xl" fw={700} mb="xs">
            Skills Assessment
          </Text>
          <Text size="md" c="dimmed">
            Evaluate current skill levels and set target goals for improvement
          </Text>
        </div>

        {/* Smart Suggestions Panel */}
        {smartSuggestions.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
          >
            <Alert
              icon={<IconSparkles size={16} />}
              color="blue"
              variant="light"
              title="Smart Skill Suggestions"
            >
              <Stack gap="sm">
                <Text size="sm">
                  Based on your role as {formData.employeeInfo?.role} in {formData.employeeInfo?.department}, 
                  here are recommended skills to consider:
                </Text>
                
                <Group gap="xs">
                  {smartSuggestions.slice(0, 8).map((suggestion) => (
                    <Tooltip key={suggestion.name} label={suggestion.description}>
                      <Chip
                        size="sm"
                        variant="outline"
                        color={suggestion.category === 'technical' ? 'blue' : 
                               suggestion.category === 'soft' ? 'green' : 
                               suggestion.category === 'leadership' ? 'orange' : 'purple'}
                        onClick={() => {
                          if (!getCurrentCategorySkills().some(s => s.skillName === suggestion.name)) {
                            addSkill(suggestion.name, suggestion.category);
                          }
                        }}
                        style={{ cursor: 'pointer' }}
                      >
                        + {suggestion.name}
                      </Chip>
                    </Tooltip>
                  ))}
                </Group>
                
                <Group justify="space-between">
                  <Text size="xs" c="dimmed">
                    Click to add suggested skills • Suggestions based on role and industry trends
                  </Text>
                  <Button
                    size="xs"
                    variant="subtle"
                    onClick={() => setShowSuggestions(!showSuggestions)}
                    rightSection={<IconTrendingUp size={12} />}
                  >
                    {showSuggestions ? 'Hide' : 'Show'} All
                  </Button>
                </Group>

                {showSuggestions && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    transition={{ duration: 0.3 }}
                  >
                    <Stack gap="xs">
                      {['technical', 'soft', 'leadership', 'compliance'].map(category => {
                        const categorySkills = smartSuggestions.filter(s => s.category === category);
                        if (categorySkills.length === 0) return null;
                        
                        return (
                          <Box key={category}>
                            <Text size="sm" fw={600} mb="xs" tt="capitalize">
                              {category} Skills:
                            </Text>
                            <Group gap="xs">
                              {categorySkills.map(skill => (
                                <Badge
                                  key={skill.name}
                                  variant="light"
                                  size="sm"
                                  style={{ cursor: 'pointer' }}
                                  onClick={() => addSkill(skill.name, skill.category)}
                                >
                                  {skill.name} ({skill.relevanceScore}%)
                                </Badge>
                              ))}
                            </Group>
                          </Box>
                        );
                      })}
                    </Stack>
                  </motion.div>
                )}
              </Stack>
            </Alert>
          </motion.div>
        )}

        {/* Enhanced Validation */}
        <EnhancedValidation
          data={skillsData}
          rules={validationRules}
          showDetails={true}
        />

        {/* Overall Competency Summary */}
        <Card withBorder p="md">
          <Group justify="space-between" mb="sm">
            <Text size="lg" fw={600}>Overall Competency Level</Text>
            <Badge size="lg" variant="filled" color="blue">
              {skillsData.overallCompetencyLevel.toFixed(1)}/5.0
            </Badge>
          </Group>
          <Progress 
            value={(skillsData.overallCompetencyLevel / 5) * 100} 
            size="lg" 
            radius="xl" 
            color="blue"
            animated
          />
          <Text size="sm" c="dimmed" mt="xs">
            Based on {[
              ...(skillsData.technicalSkills || []),
              ...(skillsData.softSkills || []),
              ...(skillsData.leadershipSkills || []),
              ...(skillsData.complianceSkills || [])
            ].length} assessed skills
          </Text>
        </Card>

        {/* Skills Categories */}
        <Tabs value={activeTab} onChange={(value) => setActiveTab(value || 'technical')}>
          <Tabs.List>
            {skillCategories.map(category => {
              const Icon = category.icon;
              const skills = skillsData[`${category.value}Skills` as keyof SkillsAssessmentData] as SkillRating[] | undefined;
              const skillCount = skills?.length || 0;
              
              return (
                <Tabs.Tab 
                  key={category.value} 
                  value={category.value}
                  leftSection={<Icon size={16} />}
                >
                  {category.label}
                  {skillCount > 0 && (
                    <Badge size="xs" ml="xs" color={category.color}>
                      {skillCount}
                    </Badge>
                  )}
                </Tabs.Tab>
              );
            })}
          </Tabs.List>

          {skillCategories.map(category => (
            <Tabs.Panel key={category.value} value={category.value} pt="md">
              <Stack gap="md">
                {/* Add Skills Section */}
                <Card withBorder p="md">
                  <Group justify="space-between" mb="sm">
                    <Text size="md" fw={600}>
                      Add {category.label}
                    </Text>
                    <Button
                      size="xs"
                      leftSection={<IconPlus size={14} />}
                      onClick={() => setAddSkillModal(true)}
                    >
                      Custom Skill
                    </Button>
                  </Group>
                  
                  <Group gap="xs" mb="sm">
                    {predefinedSkills[category.value as keyof typeof predefinedSkills]
                      .filter(skill => !getCurrentCategorySkills().some(s => s.skillName === skill))
                      .slice(0, 8)
                      .map(skill => (
                        <Button
                          key={skill}
                          variant="light"
                          size="xs"
                          onClick={() => addPredefinedSkill(skill)}
                        >
                          + {skill}
                        </Button>
                      ))
                    }
                  </Group>
                </Card>

                {/* Skills List */}
                <AnimatePresence>
                  {getCurrentCategorySkills().map((skill, index) => (
                    <motion.div
                      key={skill.id}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      exit={{ opacity: 0, x: 20 }}
                      transition={{ delay: index * 0.05 }}
                    >
                      <Card withBorder p="md">
                        <Group justify="space-between" mb="sm">
                          <div style={{ flex: 1 }}>
                            <Group gap="xs">
                              <Text fw={600}>{skill.skillName}</Text>
                              <Badge 
                                size="xs" 
                                color={getImportanceColor(skill.importance)}
                              >
                                {skill.importance}
                              </Badge>
                            </Group>
                          </div>
                          <ActionIcon
                            color="red"
                            variant="subtle"
                            onClick={() => removeSkill(skill.id)}
                          >
                            <IconX size={16} />
                          </ActionIcon>
                        </Group>

                        <Grid>
                          <Grid.Col span={4}>
                            <Text size="sm" fw={500} mb="xs">
                              Current Level
                            </Text>
                            <Group gap="xs">
                              <Rating
                                value={skill.currentLevel}
                                onChange={(value) => updateSkillRating(skill.id, 'currentLevel', value)}
                                count={5}
                                size="sm"
                              />
                              <Text size="sm" c="dimmed">
                                {getSkillLevelText(skill.currentLevel)}
                              </Text>
                            </Group>
                          </Grid.Col>

                          <Grid.Col span={4}>
                            <Text size="sm" fw={500} mb="xs">
                              Target Level
                            </Text>
                            <Group gap="xs">
                              <Rating
                                value={skill.targetLevel}
                                onChange={(value) => updateSkillRating(skill.id, 'targetLevel', value)}
                                count={5}
                                size="sm"
                                color="orange"
                              />
                              <Text size="sm" c="dimmed">
                                {getSkillLevelText(skill.targetLevel)}
                              </Text>
                            </Group>
                          </Grid.Col>

                          <Grid.Col span={4}>
                            <Text size="sm" fw={500} mb="xs">
                              Importance
                            </Text>
                            <Select
                              size="sm"
                              data={[
                                { value: 'low', label: 'Low' },
                                { value: 'medium', label: 'Medium' },
                                { value: 'high', label: 'High' },
                                { value: 'critical', label: 'Critical' }
                              ]}
                              value={skill.importance}
                              onChange={(value) => updateSkillRating(skill.id, 'importance', value)}
                            />
                          </Grid.Col>

                          <Grid.Col span={12}>
                            <Textarea
                              placeholder="Notes about this skill..."
                              size="sm"
                              autosize
                              minRows={1}
                              maxRows={3}
                              value={skill.notes || ''}
                              onChange={(e) => updateSkillRating(skill.id, 'notes', e.target.value)}
                            />
                          </Grid.Col>
                        </Grid>

                        {/* Skill Gap Indicator */}
                        {skill.targetLevel > skill.currentLevel && (
                          <Box mt="sm" p="xs" bg="orange.0" style={{ borderRadius: '4px' }}>
                            <Text size="xs" c="orange.7">
                              Gap: {skill.targetLevel - skill.currentLevel} level(s) to reach target
                            </Text>
                          </Box>
                        )}
                      </Card>
                    </motion.div>
                  ))}
                </AnimatePresence>

                {getCurrentCategorySkills().length === 0 && (
                  <Card withBorder p="xl" style={{ textAlign: 'center' }}>
                    <Text c="dimmed" size="lg">
                      No {category.label.toLowerCase()} added yet
                    </Text>
                    <Text c="dimmed" size="sm">
                      Add skills from the suggestions above or create custom ones
                    </Text>
                  </Card>
                )}
              </Stack>
            </Tabs.Panel>
          ))}
        </Tabs>

        {/* Add Custom Skill Modal */}
        <Modal
          opened={addSkillModal}
          onClose={() => setAddSkillModal(false)}
          title="Add Custom Skill"
          size="md"
        >
          <Stack gap="md">
            <TextInput
              label="Skill Name"
              placeholder="Enter skill name"
              value={newSkillName}
              onChange={(e) => setNewSkillName(e.target.value)}
            />
            
            <Select
              label="Category"
              data={skillCategories.map(cat => ({ value: cat.value, label: cat.label }))}
              value={newSkillCategory}
              onChange={(value) => setNewSkillCategory(value || 'technical')}
            />

            <Group justify="flex-end">
              <Button variant="outline" onClick={() => setAddSkillModal(false)}>
                Cancel
              </Button>
              <Button onClick={handleAddCustomSkill}>
                Add Skill
              </Button>
            </Group>
          </Stack>
        </Modal>
      </Stack>
    </motion.div>
  );
};

export default SkillsAssessmentStep;