import React, { useState } from 'react';
import { 
  Group, 
  Text, 
  Button, 
  Menu, 
  ActionIcon, 
  Badge, 
  Modal, 
  Stack, 
  TextInput, 
  Textarea, 
  MultiSelect,
  Switch,
  Select,
  Alert,
  Tooltip,
  Box
} from '@mantine/core';
import { useMediaQuery } from '@mantine/hooks';
import { 
  IconDownload, 
  IconShare, 
  IconMail, 
  IconFileTypePdf, 
  IconFileTypeJson, 
  IconFileTypeCsv,
  IconChevronDown,
  IconSparkles,
  IconSend,
  IconCheck
} from '@tabler/icons-react';
import { motion } from 'framer-motion';
import { useTrainingFormStore } from '../../stores/trainingFormStore';
import ExportService, { ExportOptions, ShareOptions } from '../../services/exportService';
import { notifications } from '@mantine/notifications';

interface EnhancedHeaderProps {
  title: string;
  subtitle?: string;
  showExportOptions?: boolean;
  showShareOptions?: boolean;
}

export const EnhancedHeader: React.FC<EnhancedHeaderProps> = ({
  title,
  subtitle,
  showExportOptions = true,
  showShareOptions = true
}) => {
  const { formData, approvalStatus } = useTrainingFormStore();
  const [exportModal, setExportModal] = useState(false);
  const [shareModal, setShareModal] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const [isSharing, setIsSharing] = useState(false);

  // Mobile responsiveness
  const isMobile = useMediaQuery('(max-width: 768px)');

  // Export options state
  const [exportOptions, setExportOptions] = useState<ExportOptions>({
    format: 'pdf',
    includePersonalInfo: true,
    includeSkillsDetails: true,
    includeApprovalHistory: true,
    template: 'detailed'
  });

  // Share options state
  const [shareOptions, setShareOptions] = useState<ShareOptions>({
    recipients: [],
    subject: '',
    message: '',
    includeAttachment: true
  });

  const handleExport = async () => {
    if (!formData.employeeInfo?.name) {
      notifications.show({
        title: 'Export Error',
        message: 'Please complete employee information before exporting',
        color: 'red',
        icon: <IconDownload />
      });
      return;
    }

    setIsExporting(true);
    
    try {
      let blob: Blob;
      let filename: string;

      switch (exportOptions.format) {
        case 'pdf':
          blob = await ExportService.generatePDF(formData, exportOptions);
          filename = ExportService.generateFilename(formData, 'pdf');
          break;
        case 'json':
          blob = ExportService.exportJSON(formData);
          filename = ExportService.generateFilename(formData, 'json');
          break;
        case 'csv':
          blob = ExportService.exportCSV(formData);
          filename = ExportService.generateFilename(formData, 'csv');
          break;
        default:
          throw new Error('Unsupported export format');
      }

      ExportService.downloadFile(blob, filename);
      
      notifications.show({
        title: 'Export Successful',
        message: `Report downloaded as ${filename}`,
        color: 'green',
        icon: <IconCheck />
      });
      
      setExportModal(false);
    } catch (error) {
      notifications.show({
        title: 'Export Failed',
        message: 'Failed to generate report. Please try again.',
        color: 'red',
        icon: <IconDownload />
      });
    } finally {
      setIsExporting(false);
    }
  };

  const handleShare = async () => {
    if (!formData.employeeInfo?.name) {
      notifications.show({
        title: 'Share Error',
        message: 'Please complete employee information before sharing',
        color: 'red',
        icon: <IconShare />
      });
      return;
    }

    if (shareOptions.recipients.length === 0) {
      notifications.show({
        title: 'Share Error',
        message: 'Please add at least one recipient',
        color: 'red',
        icon: <IconMail />
      });
      return;
    }

    setIsSharing(true);
    
    try {
      const result = await ExportService.shareViaEmail(formData, shareOptions);
      
      if (result.success) {
        notifications.show({
          title: 'Share Successful',
          message: result.message,
          color: 'green',
          icon: <IconCheck />
        });
        setShareModal(false);
      } else {
        throw new Error(result.message);
      }
    } catch (error) {
      notifications.show({
        title: 'Share Failed',
        message: 'Failed to share report. Please try again.',
        color: 'red',
        icon: <IconSend />
      });
    } finally {
      setIsSharing(false);
    }
  };

  const getFormatIcon = (format: string) => {
    switch (format) {
      case 'pdf': return <IconFileTypePdf size={16} />;
      case 'json': return <IconFileTypeJson size={16} />;
      case 'csv': return <IconFileTypeCsv size={16} />;
      default: return <IconDownload size={16} />;
    }
  };

  return (
    <>
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <Box mb="xl" p={isMobile ? "sm" : "md"} bg="white" style={{ borderRadius: '12px', border: '1px solid #e5e7eb' }}>
          {isMobile ? (
            // Mobile Layout - Stacked
            <Stack gap="md">
              <div>
                <Text size="lg" fw={700} c="gray.8">
                  {title}
                </Text>
                {subtitle && (
                  <Text size="sm" c="dimmed">
                    {subtitle}
                  </Text>
                )}
              </div>
              
              <Group justify="space-between" align="center">
                <Group gap="xs">
                  {formData.employeeInfo?.name && (
                    <Badge
                      size="sm"
                      variant="light"
                      color="blue"
                      leftSection={<IconSparkles size={12} />}
                    >
                      {formData.employeeInfo.name.split(' ')[0]} {/* First name only on mobile */}
                    </Badge>
                  )}
                  
                  <Badge
                    size="xs"
                    variant="filled"
                    color={approvalStatus === 'draft' ? 'blue' : 
                           approvalStatus === 'submitted' ? 'orange' : 
                           approvalStatus === 'approved' ? 'green' : 'gray'}
                  >
                    {approvalStatus.replace('_', ' ').toUpperCase()}
                  </Badge>
                </Group>

                <Group gap="xs">
                  {showExportOptions && (
                    <ActionIcon
                      variant="light"
                      disabled={!formData.employeeInfo?.name}
                      onClick={() => setExportModal(true)}
                    >
                      <IconDownload size={16} />
                    </ActionIcon>
                  )}

                  {showShareOptions && (
                    <ActionIcon
                      variant="outline"
                      disabled={!formData.employeeInfo?.name}
                      onClick={() => setShareModal(true)}
                    >
                      <IconShare size={16} />
                    </ActionIcon>
                  )}
                </Group>
              </Group>
            </Stack>
          ) : (
            // Desktop Layout - Side by side
            <Group justify="space-between" align="center">
              <div>
                <Group gap="md" align="center">
                  <div>
                    <Text size="xl" fw={700} c="gray.8">
                      {title}
                    </Text>
                    {subtitle && (
                      <Text size="sm" c="dimmed">
                        {subtitle}
                      </Text>
                    )}
                  </div>
                  
                  {formData.employeeInfo?.name && (
                    <Badge
                      size="lg"
                      variant="light"
                      color="blue"
                      leftSection={<IconSparkles size={14} />}
                    >
                      {formData.employeeInfo.name}
                    </Badge>
                  )}
                  
                  <Badge
                    size="sm"
                    variant="filled"
                    color={approvalStatus === 'draft' ? 'blue' : 
                           approvalStatus === 'submitted' ? 'orange' : 
                           approvalStatus === 'approved' ? 'green' : 'gray'}
                  >
                    {approvalStatus.replace('_', ' ').toUpperCase()}
                  </Badge>
                </Group>
              </div>

              <Group gap="sm">
                {showExportOptions && (
                  <Menu shadow="md" width={200}>
                    <Menu.Target>
                      <Button
                        variant="light"
                        leftSection={<IconDownload size={16} />}
                        rightSection={<IconChevronDown size={14} />}
                        disabled={!formData.employeeInfo?.name}
                      >
                        Export
                      </Button>
                    </Menu.Target>

                <Menu.Dropdown>
                  <Menu.Label>Quick Export</Menu.Label>
                  <Menu.Item
                    leftSection={<IconFileTypePdf size={16} />}
                    onClick={() => {
                      setExportOptions({ ...exportOptions, format: 'pdf' });
                      handleExport();
                    }}
                  >
                    Export as PDF
                  </Menu.Item>
                  <Menu.Item
                    leftSection={<IconFileTypeJson size={16} />}
                    onClick={() => {
                      setExportOptions({ ...exportOptions, format: 'json' });
                      handleExport();
                    }}
                  >
                    Export as JSON
                  </Menu.Item>
                  <Menu.Item
                    leftSection={<IconFileTypeCsv size={16} />}
                    onClick={() => {
                      setExportOptions({ ...exportOptions, format: 'csv' });
                      handleExport();
                    }}
                  >
                    Export as CSV
                  </Menu.Item>
                  
                  <Menu.Divider />
                  <Menu.Item
                    leftSection={<IconSparkles size={16} />}
                    onClick={() => setExportModal(true)}
                  >
                    Advanced Export...
                  </Menu.Item>
                </Menu.Dropdown>
              </Menu>
            )}

            {showShareOptions && (
              <Tooltip label="Share report via email">
                <Button
                  variant="outline"
                  leftSection={<IconShare size={16} />}
                  onClick={() => setShareModal(true)}
                  disabled={!formData.employeeInfo?.name}
                >
                  Share
                </Button>
              </Tooltip>
            )}
          </Group>
            </Group>
        )}
        </Box>
      </motion.div>

      {/* Advanced Export Modal */}
      <Modal
        opened={exportModal}
        onClose={() => setExportModal(false)}
        title="Advanced Export Options"
        size="md"
      >
        <Stack gap="md">
          <Select
            label="Export Format"
            value={exportOptions.format}
            onChange={(value) => setExportOptions({ ...exportOptions, format: value as any })}
            data={[
              { value: 'pdf', label: 'PDF Report' },
              { value: 'json', label: 'JSON Data' },
              { value: 'csv', label: 'CSV Spreadsheet' }
            ]}
            leftSection={getFormatIcon(exportOptions.format)}
          />

          <Select
            label="Report Template"
            value={exportOptions.template}
            onChange={(value) => setExportOptions({ ...exportOptions, template: value as any })}
            data={[
              { value: 'detailed', label: 'Detailed Report (Complete)' },
              { value: 'summary', label: 'Summary Report (Key Points)' },
              { value: 'executive', label: 'Executive Summary (Overview)' }
            ]}
          />

          <Stack gap="xs">
            <Text size="sm" fw={600}>Include Sections:</Text>
            
            <Switch
              label="Personal Information"
              checked={exportOptions.includePersonalInfo}
              onChange={(event) => setExportOptions({ 
                ...exportOptions, 
                includePersonalInfo: event.currentTarget.checked 
              })}
            />
            
            <Switch
              label="Skills Details"
              checked={exportOptions.includeSkillsDetails}
              onChange={(event) => setExportOptions({ 
                ...exportOptions, 
                includeSkillsDetails: event.currentTarget.checked 
              })}
            />
            
            <Switch
              label="Approval History"
              checked={exportOptions.includeApprovalHistory}
              onChange={(event) => setExportOptions({ 
                ...exportOptions, 
                includeApprovalHistory: event.currentTarget.checked 
              })}
            />
          </Stack>

          <Group justify="flex-end" mt="md">
            <Button variant="outline" onClick={() => setExportModal(false)}>
              Cancel
            </Button>
            <Button
              onClick={handleExport}
              loading={isExporting}
              leftSection={getFormatIcon(exportOptions.format)}
            >
              Export Report
            </Button>
          </Group>
        </Stack>
      </Modal>

      {/* Share Modal */}
      <Modal
        opened={shareModal}
        onClose={() => setShareModal(false)}
        title="Share Training Needs Analysis"
        size="md"
      >
        <Stack gap="md">
          <Alert color="blue" variant="light">
            Share this training needs analysis with managers, HR, or other stakeholders.
          </Alert>

          <MultiSelect
            label="Recipients"
            placeholder="Enter email addresses"
            data={[
              '<EMAIL>',
              '<EMAIL>',
              '<EMAIL>',
              '<EMAIL>'
            ]}
            value={shareOptions.recipients}
            onChange={(value) => setShareOptions({ ...shareOptions, recipients: value })}
            searchable
            creatable
            getCreateLabel={(query) => `+ Add ${query}`}
            onCreate={(query) => {
              // Basic email validation
              const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
              return emailRegex.test(query) ? query : null;
            }}
          />

          <TextInput
            label="Subject"
            placeholder="Training Needs Analysis Report"
            value={shareOptions.subject}
            onChange={(event) => setShareOptions({ 
              ...shareOptions, 
              subject: event.currentTarget.value 
            })}
          />

          <Textarea
            label="Message"
            placeholder="Please find the attached training needs analysis report..."
            value={shareOptions.message}
            onChange={(event) => setShareOptions({ 
              ...shareOptions, 
              message: event.currentTarget.value 
            })}
            rows={4}
          />

          <Switch
            label="Include report as attachment"
            checked={shareOptions.includeAttachment}
            onChange={(event) => setShareOptions({ 
              ...shareOptions, 
              includeAttachment: event.currentTarget.checked 
            })}
          />

          <Group justify="flex-end" mt="md">
            <Button variant="outline" onClick={() => setShareModal(false)}>
              Cancel
            </Button>
            <Button
              onClick={handleShare}
              loading={isSharing}
              leftSection={<IconSend size={16} />}
            >
              Send Report
            </Button>
          </Group>
        </Stack>
      </Modal>
    </>
  );
};

export default EnhancedHeader;