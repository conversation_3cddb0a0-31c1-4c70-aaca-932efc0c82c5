import React from 'react';
import { Box, Container, useMatches } from '@mantine/core';
import { useMediaQuery } from '@mantine/hooks';

interface ResponsiveLayoutProps {
  children: React.ReactNode;
  maxWidth?: string | number;
  padding?: string | number;
}

export const ResponsiveLayout: React.FC<ResponsiveLayoutProps> = ({
  children,
  maxWidth = 'lg',
  padding
}) => {
  const isMobile = useMediaQuery('(max-width: 768px)');
  const isTablet = useMediaQuery('(max-width: 1024px)');
  
  // Responsive padding
  const responsivePadding = useMatches({
    base: 'xs',  // Mobile
    sm: 'md',    // Small screens
    md: 'lg',    // Medium screens  
    lg: 'xl'     // Large screens
  });

  return (
    <Container 
      size={maxWidth} 
      p={padding || responsivePadding}
      style={{
        width: '100%',
        minHeight: '100vh',
        display: 'flex',
        flexDirection: 'column'
      }}
    >
      <Box
        style={{
          flex: 1,
          width: '100%',
          // Ensure content doesn't overflow on mobile
          overflowX: 'hidden',
          // Better touch targets on mobile
          touchAction: 'manipulation'
        }}
      >
        {children}
      </Box>
    </Container>
  );
};

export default ResponsiveLayout;