/**
 * Components Index - Export all main components
 */

// UI Components
export * from './ui'

// Feature Components - explicit re-exports to resolve conflicts
export { 
  LighthouseMain, 
  default as Lighthouse,
  ProjectContext,
  NavigationSystem,
  ModuleContainer,
  Dashboard,
  DashboardLayout,
  LighthouseHeader,
  LighthouseSidebar,
  KnowledgeHub,
  ResearchStudio,
  AgentWorkspace,
  ContextualChat,
  SourceManager,
  AnalyticsDashboard,
  AIInsights,
  EnhancedUIDemo,
  DashboardDemo,
  DemoLauncher,
  useLighthouseStore,
  EnhancedTooltip,
  ProgressIndicator as LighthouseProgressIndicator
} from './lighthouse'

export * from './auth'
export * from './ega-academy'
export * from './wins-of-week'
export * from './admin'
export * from './animate-ui'

// Vendor Management Components
export * from './vendor-management';

// Training Needs Analysis Components - explicit re-export for ProgressIndicator
export {
  ProgressIndicator as TrainingProgressIndicator
} from './training-needs-analysis';
export * from './training-needs-analysis';

// Training Budget Analysis Components
export * from './training-budget-analysis';

// Default Components
export { default as NotFound } from './NotFound'
export { default as DefaultCatchBoundary } from './DefaultCatchBoundary'
export { default as PostError } from './PostError'
export { default as UserError } from './UserError'
