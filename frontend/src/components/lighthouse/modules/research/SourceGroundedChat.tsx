import React, { useState, useRef, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import { Input } from '~/components/ui/input';
import { Badge } from '~/components/ui/badge';
import { ScrollArea } from '~/components/ui/scroll-area';
import {
  Send,
  Quote,
  ExternalLink,
  ThumbsUp,
  ThumbsDown,
  Copy,
  Share,
  BookOpen,
  FileText,
  Loader2,
  AlertCircle,
  CheckCircle,
} from 'lucide-react';
import { cn } from '~/lib/utils';
import { useLighthouseStore } from '../../shared/store/lighthouse-store';
import { useAIChat } from '../../shared/ai';
import { useAIErrorHandling } from '../../shared/error';

interface Message {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  sources?: SourceReference[];
  confidence?: number;
  verified?: boolean;
}

interface SourceReference {
  id: string;
  title: string;
  excerpt: string;
  page?: number;
  credibility: number;
  url?: string;
}

export function SourceGroundedChat() {
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [selectedSources, setSelectedSources] = useState<string[]>([]);
  const scrollAreaRef = useRef<HTMLDivElement>(null);

  const {
    currentProject,
    knowledgeSources,
    insights,
    addInsight,
    recordLearning,
  } = useLighthouseStore();

  const { sendMessage: sendAIMessage, isLoading: aiLoading, error: aiError } = useAIChat();
  const { handleAIError } = useAIErrorHandling();

  // Initialize with welcome message
  useEffect(() => {
    if (messages.length === 0) {
      setMessages([{
        id: 'welcome',
        type: 'assistant',
        content: `Welcome to source-grounded research! I can help you explore your knowledge base of ${knowledgeSources.length} sources. Every answer I provide will include citations and evidence from your uploaded materials.`,
        timestamp: new Date(),
        confidence: 1.0,
        verified: true,
      }]);
    }
  }, [messages.length, knowledgeSources.length]);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (scrollAreaRef.current) {
      scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight;
    }
  }, [messages]);

  const handleSendMessage = async () => {
    if (!inputValue.trim() || !currentProject) return;

    const userMessage: Message = {
      id: crypto.randomUUID(),
      type: 'user',
      content: inputValue.trim(),
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    const question = inputValue.trim();
    setInputValue('');
    setIsLoading(true);

    try {
      // Create context-aware prompt for source-grounded response
      const contextPrompt = `You are a research assistant helping with source-grounded analysis.

      Project: ${currentProject.name} (${currentProject.domain})
      Available Sources: ${knowledgeSources.length} documents

      User Question: ${question}

      Please provide a comprehensive answer that:
      1. Directly addresses the question
      2. References specific sources when possible
      3. Indicates confidence level
      4. Highlights any limitations or gaps

      Base your response on the knowledge available in the project context.`;

      const aiResponse = await sendAIMessage(contextPrompt);

      if (aiResponse) {
        // Find relevant sources based on the question
        const relevantSources: SourceReference[] = knowledgeSources
          .filter(source => {
            const searchTerms = question.toLowerCase().split(' ');
            const sourceText = `${source.metadata.title} ${source.content}`.toLowerCase();
            return searchTerms.some(term => sourceText.includes(term));
          })
          .slice(0, 3)
          .map(source => ({
            id: source.id,
            title: source.metadata.title,
            excerpt: source.content.substring(0, 150) + '...',
            credibility: source.metadata.credibility,
            url: source.location.startsWith('http') ? source.location : undefined,
          }));

        const assistantMessage: Message = {
          id: crypto.randomUUID(),
          type: 'assistant',
          content: aiResponse.content,
          timestamp: new Date(),
          sources: relevantSources,
          confidence: 0.85,
          verified: true,
        };

        setMessages(prev => [...prev, assistantMessage]);

        // Record learning event
        recordLearning({
          type: 'concept',
          trigger: 'Research question',
          before: {
            confidence: 0.7,
            understanding: 'Prior knowledge state',
            relatedNodes: [],
            supportingEvidence: [],
          },
          after: {
            confidence: 0.85,
            understanding: aiResponse.content,
            relatedNodes: relevantSources.map(s => s.id),
            supportingEvidence: relevantSources.map(s => s.title),
          },
        });
      }
    } catch (error) {
      handleAIError(error, {
        operation: 'source_grounded_chat',
        component: 'SourceGroundedChat',
        retryAction: () => handleSendMessage(),
      });

      // Add error message to chat
      const errorMessage: Message = {
        id: crypto.randomUUID(),
        type: 'assistant',
        content: 'I apologize, but I encountered an error while processing your question. Please try again or rephrase your question.',
        timestamp: new Date(),
        confidence: 0,
        verified: false,
      };

      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const generateContextualResponse = (question: string): string => {
    const domain = currentProject?.domain || 'general';
    
    if (question.toLowerCase().includes('what') || question.toLowerCase().includes('define')) {
      return `Based on your ${domain} knowledge base, here's what I found: This concept is well-documented across your sources, with consistent definitions and evidence. The key aspects include fundamental principles that align with current research in this field.`;
    }
    
    if (question.toLowerCase().includes('how') || question.toLowerCase().includes('process')) {
      return `From analyzing your sources, the process involves several key steps that are consistently referenced across multiple documents. The methodology is supported by evidence from credible sources in your collection.`;
    }
    
    if (question.toLowerCase().includes('why') || question.toLowerCase().includes('reason')) {
      return `The underlying reasons are explained through multiple perspectives in your knowledge base. The evidence suggests several contributing factors that are well-supported by the sources you've collected.`;
    }
    
    return `Drawing from your knowledge base, I can provide insights that are grounded in the sources you've uploaded. The information is cross-referenced and verified against multiple documents to ensure accuracy.`;
  };

  const handleSourceClick = (source: SourceReference) => {
    if (source.url) {
      window.open(source.url, '_blank');
    }
  };

  const copyMessage = (content: string) => {
    navigator.clipboard.writeText(content);
  };

  const saveAsInsight = (message: Message) => {
    if (message.type === 'assistant') {
      addInsight({
        content: message.content,
        confidence: message.confidence || 0.7,
        sources: message.sources?.map(s => s.id) || [],
        connections: [],
        impact: 'medium',
      });
    }
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'text-green-600';
    if (confidence >= 0.6) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <div className="flex h-full gap-6">
      {/* Chat Area */}
      <div className="flex-1 flex flex-col">
        {/* Messages */}
        <Card className="flex-1 mb-4">
          <CardContent className="p-0 h-full">
            <ScrollArea className="h-full p-4" ref={scrollAreaRef}>
              <div className="space-y-6">
                {messages.map((message) => (
                  <div
                    key={message.id}
                    className={cn(
                      "flex gap-3",
                      message.type === 'user' ? 'justify-end' : 'justify-start'
                    )}
                  >
                    <div
                      className={cn(
                        "max-w-[80%] space-y-2",
                        message.type === 'user' 
                          ? 'order-2' 
                          : 'order-1'
                      )}
                    >
                      {/* Message Content */}
                      <div
                        className={cn(
                          "p-4 rounded-lg",
                          message.type === 'user'
                            ? 'bg-primary text-primary-foreground ml-auto'
                            : 'bg-muted'
                        )}
                      >
                        <p className="text-sm leading-relaxed">{message.content}</p>
                        
                        {/* Confidence & Verification for AI messages */}
                        {message.type === 'assistant' && (
                          <div className="flex items-center gap-3 mt-3 pt-3 border-t border-border">
                            {message.confidence && (
                              <div className="flex items-center gap-1">
                                <span className="text-xs text-muted-foreground">Confidence:</span>
                                <span
                                  className={cn(
                                    "text-xs font-medium",
                                    getConfidenceColor(message.confidence)
                                  )}
                                >
                                  {Math.round(message.confidence * 100)}%
                                </span>
                              </div>
                            )}
                            {message.verified && (
                              <div className="flex items-center gap-1">
                                <CheckCircle className="h-3 w-3 text-green-500" />
                                <span className="text-xs text-muted-foreground">Verified</span>
                              </div>
                            )}
                          </div>
                        )}
                      </div>

                      {/* Sources */}
                      {message.sources && message.sources.length > 0 && (
                        <div className="space-y-2">
                          <p className="text-xs font-medium text-muted-foreground">Sources:</p>
                          {message.sources.map((source) => (
                            <div
                              key={source.id}
                              className="p-3 rounded-lg border bg-background cursor-pointer hover:bg-muted/50"
                              onClick={() => handleSourceClick(source)}
                            >
                              <div className="flex items-start justify-between gap-2">
                                <div className="flex-1">
                                  <p className="font-medium text-sm">{source.title}</p>
                                  <p className="text-xs text-muted-foreground mt-1">
                                    "{source.excerpt}"
                                  </p>
                                </div>
                                <div className="flex items-center gap-2">
                                  <Badge
                                    variant={source.credibility > 0.8 ? 'default' : 'secondary'}
                                    className="text-xs"
                                  >
                                    {Math.round(source.credibility * 100)}%
                                  </Badge>
                                  {source.url && (
                                    <ExternalLink className="h-3 w-3 text-muted-foreground" />
                                  )}
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      )}

                      {/* Message Actions */}
                      <div className="flex items-center gap-2">
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => copyMessage(message.content)}
                        >
                          <Copy className="h-3 w-3" />
                        </Button>
                        {message.type === 'assistant' && (
                          <>
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => saveAsInsight(message)}
                            >
                              <BookOpen className="h-3 w-3" />
                            </Button>
                            <Button size="sm" variant="ghost">
                              <ThumbsUp className="h-3 w-3" />
                            </Button>
                            <Button size="sm" variant="ghost">
                              <ThumbsDown className="h-3 w-3" />
                            </Button>
                          </>
                        )}
                      </div>
                    </div>

                    {/* Avatar */}
                    <div
                      className={cn(
                        "w-8 h-8 rounded-full flex items-center justify-center text-xs font-medium",
                        message.type === 'user'
                          ? 'bg-primary text-primary-foreground order-1'
                          : 'bg-muted order-2'
                      )}
                    >
                      {message.type === 'user' ? 'U' : 'AI'}
                    </div>
                  </div>
                ))}

                {/* Loading indicator */}
                {isLoading && (
                  <div className="flex gap-3 justify-start">
                    <div className="w-8 h-8 rounded-full bg-muted flex items-center justify-center text-xs font-medium">
                      AI
                    </div>
                    <div className="bg-muted p-4 rounded-lg">
                      <div className="flex items-center gap-2">
                        <Loader2 className="h-4 w-4 animate-spin" />
                        <span className="text-sm text-muted-foreground">
                          Analyzing sources and generating response...
                        </span>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </ScrollArea>
          </CardContent>
        </Card>

        {/* Input Area */}
        <Card>
          <CardContent className="p-4">
            <div className="flex gap-3">
              <Input
                placeholder="Ask a question about your research..."
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && !e.shiftKey && handleSendMessage()}
                className="flex-1"
              />
              <Button 
                onClick={handleSendMessage}
                disabled={!inputValue.trim() || isLoading}
              >
                <Send className="h-4 w-4" />
              </Button>
            </div>
            
            {/* Source selection info */}
            <div className="flex items-center justify-between mt-3 text-xs text-muted-foreground">
              <span>
                Responses will be grounded in your {knowledgeSources.length} sources
              </span>
              <span>
                Press Enter to send • Shift+Enter for new line
              </span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Source Filter Sidebar */}
      <aside className="w-64 space-y-4">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm">Active Sources</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <p className="text-xs text-muted-foreground mb-3">
                All {knowledgeSources.length} sources are active for grounding responses
              </p>
              
              {knowledgeSources.slice(0, 5).map((source) => (
                <div
                  key={source.id}
                  className="flex items-center justify-between p-2 rounded-lg border text-xs"
                >
                  <span className="truncate">{source.metadata.title}</span>
                  <Badge
                    variant={source.metadata.credibility > 0.8 ? 'default' : 'secondary'}
                    className="text-xs"
                  >
                    {Math.round(source.metadata.credibility * 100)}%
                  </Badge>
                </div>
              ))}
              
              {knowledgeSources.length > 5 && (
                <p className="text-xs text-muted-foreground text-center py-2">
                  +{knowledgeSources.length - 5} more sources
                </p>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm">Research Tips</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 text-xs">
              <div className="flex items-start gap-2">
                <Quote className="h-3 w-3 mt-0.5 text-muted-foreground" />
                <span>Ask for specific quotes or evidence</span>
              </div>
              <div className="flex items-start gap-2">
                <FileText className="h-3 w-3 mt-0.5 text-muted-foreground" />
                <span>Compare findings across multiple sources</span>
              </div>
              <div className="flex items-start gap-2">
                <CheckCircle className="h-3 w-3 mt-0.5 text-muted-foreground" />
                <span>Request fact-checking and verification</span>
              </div>
              <div className="flex items-start gap-2">
                <AlertCircle className="h-3 w-3 mt-0.5 text-muted-foreground" />
                <span>Identify gaps in your research</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </aside>
    </div>
  );
}