import { useState, useEffect, useCallback, useRef } from 'react';
import { 
  getWebSocketService, 
  LighthouseEvent, 
  LighthouseEventType, 
  ConnectionState,
  EventListener 
} from './WebSocketService';
import { useLighthouseStore } from '../store/lighthouse-store';

// Real-time data hook state
interface RealTimeDataState {
  isConnected: boolean;
  connectionState: ConnectionState;
  events: LighthouseEvent[];
  lastEvent: LighthouseEvent | null;
  eventCounts: Record<LighthouseEventType, number>;
  error: string | null;
}

// Hook options
interface UseRealTimeDataOptions {
  autoConnect?: boolean;
  maxEvents?: number;
  eventTypes?: LighthouseEventType[];
  onEvent?: (event: LighthouseEvent) => void;
  onConnectionChange?: (state: ConnectionState) => void;
}

export function useRealTimeData(options: UseRealTimeDataOptions = {}) {
  const {
    autoConnect = true,
    maxEvents = 100,
    eventTypes,
    onEvent,
    onConnectionChange,
  } = options;

  const [state, setState] = useState<RealTimeDataState>({
    isConnected: false,
    connectionState: ConnectionState.DISCONNECTED,
    events: [],
    lastEvent: null,
    eventCounts: {} as Record<LighthouseEventType, number>,
    error: null,
  });

  const webSocketService = getWebSocketService();
  const unsubscribersRef = useRef<(() => void)[]>([]);
  const { 
    currentProject, 
    addKnowledgeItem, 
    addInsight, 
    recordLearning,
    updateIntelligenceContext 
  } = useLighthouseStore();

  // Handle incoming events
  const handleEvent = useCallback((event: LighthouseEvent) => {
    setState(prev => {
      const newEvents = [event, ...prev.events].slice(0, maxEvents);
      const newEventCounts = { ...prev.eventCounts };
      newEventCounts[event.type] = (newEventCounts[event.type] || 0) + 1;

      return {
        ...prev,
        events: newEvents,
        lastEvent: event,
        eventCounts: newEventCounts,
      };
    });

    // Handle specific event types
    handleSpecificEvent(event);

    // Call custom event handler
    onEvent?.(event);
  }, [maxEvents, onEvent]);

  // Handle specific event types with store updates
  const handleSpecificEvent = useCallback((event: LighthouseEvent) => {
    switch (event.type) {
      case 'knowledge_item_added':
        if (event.payload.data.knowledgeItem) {
          addKnowledgeItem(event.payload.data.knowledgeItem);
        }
        break;

      case 'insight_generated':
        if (event.payload.data.insight) {
          addInsight(event.payload.data.insight);
        }
        break;

      case 'learning_event':
        if (event.payload.data.learningEvent) {
          recordLearning(event.payload.data.learningEvent);
        }
        break;

      case 'agent_status_update':
        // Update agent status in store
        if (event.payload.data.agentId && event.payload.data.status) {
          updateIntelligenceContext({
            agentActivities: {
              [event.payload.data.agentId]: {
                status: event.payload.data.status,
                lastUpdate: event.timestamp,
                metadata: event.payload.metadata,
              }
            }
          });
        }
        break;

      case 'system_metric':
        // Handle system metrics
        if (event.payload.data.type === 'connection_state_change') {
          setState(prev => ({
            ...prev,
            connectionState: event.payload.data.state,
            isConnected: event.payload.data.state === ConnectionState.CONNECTED,
          }));
          onConnectionChange?.(event.payload.data.state);
        }
        break;

      case 'error_occurred':
        setState(prev => ({
          ...prev,
          error: event.payload.data.message || 'Unknown error occurred',
        }));
        break;
    }
  }, [addKnowledgeItem, addInsight, recordLearning, updateIntelligenceContext, onConnectionChange]);

  // Subscribe to events
  const subscribeToEvents = useCallback(() => {
    const typesToSubscribe = eventTypes || [
      'agent_status_update',
      'knowledge_item_added',
      'insight_generated',
      'learning_event',
      'project_update',
      'user_activity',
      'system_metric',
      'error_occurred',
      'recommendation_generated',
    ];

    // Clear existing subscriptions
    unsubscribersRef.current.forEach(unsubscribe => unsubscribe());
    unsubscribersRef.current = [];

    // Subscribe to each event type
    typesToSubscribe.forEach(eventType => {
      const unsubscribe = webSocketService.on(eventType, handleEvent);
      unsubscribersRef.current.push(unsubscribe);
    });
  }, [eventTypes, webSocketService, handleEvent]);

  // Connect to WebSocket
  const connect = useCallback(async () => {
    try {
      setState(prev => ({ ...prev, error: null }));
      await webSocketService.connect();
      subscribeToEvents();
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Connection failed',
      }));
    }
  }, [webSocketService, subscribeToEvents]);

  // Disconnect from WebSocket
  const disconnect = useCallback(() => {
    unsubscribersRef.current.forEach(unsubscribe => unsubscribe());
    unsubscribersRef.current = [];
    webSocketService.disconnect();
    setState(prev => ({
      ...prev,
      isConnected: false,
      connectionState: ConnectionState.DISCONNECTED,
    }));
  }, [webSocketService]);

  // Send event
  const sendEvent = useCallback((
    eventType: LighthouseEventType,
    data: any,
    metadata?: Record<string, any>
  ) => {
    if (!webSocketService.isConnected()) {
      console.warn('Cannot send event: not connected');
      return;
    }

    const eventData = {
      ...data,
      projectId: currentProject?.id,
      timestamp: new Date(),
    };

    webSocketService.emit(eventType, eventData, metadata);
  }, [webSocketService, currentProject]);

  // Clear events
  const clearEvents = useCallback(() => {
    setState(prev => ({
      ...prev,
      events: [],
      lastEvent: null,
      eventCounts: {} as Record<LighthouseEventType, number>,
      error: null,
    }));
  }, []);

  // Get events by type
  const getEventsByType = useCallback((eventType: LighthouseEventType): LighthouseEvent[] => {
    return state.events.filter(event => event.type === eventType);
  }, [state.events]);

  // Get recent events (last 5 minutes)
  const getRecentEvents = useCallback((): LighthouseEvent[] => {
    const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
    return state.events.filter(event => event.timestamp > fiveMinutesAgo);
  }, [state.events]);

  // Initialize connection
  useEffect(() => {
    if (autoConnect) {
      connect();
    }

    return () => {
      disconnect();
    };
  }, [autoConnect, connect, disconnect]);

  // Update connection state
  useEffect(() => {
    const currentState = webSocketService.getConnectionState();
    setState(prev => ({
      ...prev,
      connectionState: currentState,
      isConnected: currentState === ConnectionState.CONNECTED,
    }));
  }, [webSocketService]);

  return {
    // State
    isConnected: state.isConnected,
    connectionState: state.connectionState,
    events: state.events,
    lastEvent: state.lastEvent,
    eventCounts: state.eventCounts,
    error: state.error,

    // Actions
    connect,
    disconnect,
    sendEvent,
    clearEvents,

    // Utilities
    getEventsByType,
    getRecentEvents,
  };
}

// Specialized hooks for specific event types

// Agent activity hook
export function useAgentActivity() {
  const { events, sendEvent } = useRealTimeData({
    eventTypes: ['agent_status_update'],
  });

  const agentEvents = events.filter(e => e.type === 'agent_status_update');

  const updateAgentStatus = useCallback((agentId: string, status: string, metadata?: any) => {
    sendEvent('agent_status_update', { agentId, status }, metadata);
  }, [sendEvent]);

  return {
    agentEvents,
    updateAgentStatus,
  };
}

// Knowledge updates hook
export function useKnowledgeUpdates() {
  const { events, sendEvent } = useRealTimeData({
    eventTypes: ['knowledge_item_added', 'insight_generated'],
  });

  const knowledgeEvents = events.filter(e => 
    e.type === 'knowledge_item_added' || e.type === 'insight_generated'
  );

  const broadcastKnowledgeUpdate = useCallback((type: 'knowledge' | 'insight', data: any) => {
    const eventType = type === 'knowledge' ? 'knowledge_item_added' : 'insight_generated';
    sendEvent(eventType, data);
  }, [sendEvent]);

  return {
    knowledgeEvents,
    broadcastKnowledgeUpdate,
  };
}

// System metrics hook
export function useSystemMetrics() {
  const { events, sendEvent } = useRealTimeData({
    eventTypes: ['system_metric'],
  });

  const metricEvents = events.filter(e => e.type === 'system_metric');

  const sendMetric = useCallback((metricType: string, value: any, metadata?: any) => {
    sendEvent('system_metric', { type: metricType, value }, metadata);
  }, [sendEvent]);

  return {
    metricEvents,
    sendMetric,
  };
}

// User activity tracking hook
export function useUserActivity() {
  const { sendEvent } = useRealTimeData({
    eventTypes: ['user_activity'],
  });

  const trackActivity = useCallback((activity: string, data?: any) => {
    sendEvent('user_activity', { activity, data, timestamp: new Date() });
  }, [sendEvent]);

  const trackPageView = useCallback((page: string) => {
    trackActivity('page_view', { page });
  }, [trackActivity]);

  const trackInteraction = useCallback((element: string, action: string) => {
    trackActivity('interaction', { element, action });
  }, [trackActivity]);

  return {
    trackActivity,
    trackPageView,
    trackInteraction,
  };
}
