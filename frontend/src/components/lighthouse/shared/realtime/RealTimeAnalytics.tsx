import React, { useMemo } from 'react';
import { motion } from 'framer-motion';
import { cn } from '~/lib/utils';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { Badge } from '~/components/ui/badge';
import { Progress } from '~/components/ui/progress';
import { useRealTimeData, useSystemMetrics } from './useRealTimeData';
import { ConnectionState } from './WebSocketService';
import { AccessibilityUtils } from '~/utils/accessibilityUtils';
import { 
  ActivityIcon, 
  TrendingUpIcon, 
  UsersIcon, 
  ZapIcon,
  WifiIcon,
  WifiOffIcon,
  AlertTriangleIcon 
} from '../icons';

interface RealTimeAnalyticsProps {
  className?: string;
  showConnectionStatus?: boolean;
  showEventCounts?: boolean;
  showRecentActivity?: boolean;
  maxRecentEvents?: number;
}

export function RealTimeAnalytics({
  className,
  showConnectionStatus = true,
  showEventCounts = true,
  showRecentActivity = true,
  maxRecentEvents = 5,
}: RealTimeAnalyticsProps) {
  const {
    isConnected,
    connectionState,
    events,
    eventCounts,
    getRecentEvents,
    error,
  } = useRealTimeData();

  const { metricEvents } = useSystemMetrics();
  const prefersReducedMotion = AccessibilityUtils.prefersReducedMotion();

  const recentEvents = useMemo(() => {
    return getRecentEvents().slice(0, maxRecentEvents);
  }, [getRecentEvents, maxRecentEvents]);

  const connectionStatusConfig = useMemo(() => {
    switch (connectionState) {
      case ConnectionState.CONNECTED:
        return {
          icon: WifiIcon,
          label: 'Connected',
          color: 'text-green-500',
          bgColor: 'bg-green-50 dark:bg-green-950',
          borderColor: 'border-green-200 dark:border-green-800',
        };
      case ConnectionState.CONNECTING:
      case ConnectionState.RECONNECTING:
        return {
          icon: WifiIcon,
          label: connectionState === ConnectionState.CONNECTING ? 'Connecting...' : 'Reconnecting...',
          color: 'text-yellow-500',
          bgColor: 'bg-yellow-50 dark:bg-yellow-950',
          borderColor: 'border-yellow-200 dark:border-yellow-800',
        };
      case ConnectionState.ERROR:
        return {
          icon: AlertTriangleIcon,
          label: 'Connection Error',
          color: 'text-red-500',
          bgColor: 'bg-red-50 dark:bg-red-950',
          borderColor: 'border-red-200 dark:border-red-800',
        };
      default:
        return {
          icon: WifiOffIcon,
          label: 'Disconnected',
          color: 'text-gray-500',
          bgColor: 'bg-gray-50 dark:bg-gray-950',
          borderColor: 'border-gray-200 dark:border-gray-800',
        };
    }
  }, [connectionState]);

  const topEventTypes = useMemo(() => {
    return Object.entries(eventCounts)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 5)
      .map(([type, count]) => ({ type, count }));
  }, [eventCounts]);

  const formatEventType = (type: string): string => {
    return type
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  const getEventIcon = (type: string) => {
    switch (type) {
      case 'agent_status_update':
        return <ZapIcon size={16} className="text-blue-500" />;
      case 'knowledge_item_added':
        return <TrendingUpIcon size={16} className="text-green-500" />;
      case 'user_activity':
        return <UsersIcon size={16} className="text-purple-500" />;
      case 'system_metric':
        return <ActivityIcon size={16} className="text-orange-500" />;
      default:
        return <ActivityIcon size={16} className="text-gray-500" />;
    }
  };

  const formatTimestamp = (timestamp: Date): string => {
    const now = new Date();
    const diff = now.getTime() - timestamp.getTime();
    
    if (diff < 60000) { // Less than 1 minute
      return 'Just now';
    } else if (diff < 3600000) { // Less than 1 hour
      return `${Math.floor(diff / 60000)}m ago`;
    } else {
      return timestamp.toLocaleTimeString();
    }
  };

  return (
    <div className={cn('space-y-4', className)}>
      {/* Connection Status */}
      {showConnectionStatus && (
        <motion.div
          initial={prefersReducedMotion ? {} : { opacity: 0, y: 20 }}
          animate={prefersReducedMotion ? {} : { opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <Card className={cn(
            'transition-all duration-200',
            connectionStatusConfig.bgColor,
            connectionStatusConfig.borderColor
          )}>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <motion.div
                  animate={prefersReducedMotion ? {} : {
                    scale: isConnected ? [1, 1.1, 1] : 1,
                  }}
                  transition={{ 
                    duration: 2, 
                    repeat: isConnected ? Infinity : 0,
                    ease: 'easeInOut'
                  }}
                >
                  <connectionStatusConfig.icon 
                    size={20} 
                    className={connectionStatusConfig.color} 
                  />
                </motion.div>
                
                <div className="flex-1">
                  <div className="flex items-center gap-2">
                    <span className="font-medium">{connectionStatusConfig.label}</span>
                    {isConnected && (
                      <Badge variant="secondary" className="text-xs">
                        Live
                      </Badge>
                    )}
                  </div>
                  
                  {error && (
                    <p className="text-sm text-red-600 dark:text-red-400 mt-1">
                      {error}
                    </p>
                  )}
                </div>

                {isConnected && (
                  <div className="text-right text-sm text-muted-foreground">
                    <div>{events.length} events</div>
                    <div>{recentEvents.length} recent</div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}

      {/* Event Counts */}
      {showEventCounts && topEventTypes.length > 0 && (
        <motion.div
          initial={prefersReducedMotion ? {} : { opacity: 0, y: 20 }}
          animate={prefersReducedMotion ? {} : { opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.1 }}
        >
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium flex items-center gap-2">
                <ActivityIcon size={16} />
                Event Activity
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {topEventTypes.map(({ type, count }, index) => {
                const percentage = Math.round((count / Math.max(...topEventTypes.map(t => t.count))) * 100);
                
                return (
                  <motion.div
                    key={type}
                    initial={prefersReducedMotion ? {} : { opacity: 0, x: -20 }}
                    animate={prefersReducedMotion ? {} : { opacity: 1, x: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.05 }}
                    className="space-y-2"
                  >
                    <div className="flex items-center justify-between text-sm">
                      <div className="flex items-center gap-2">
                        {getEventIcon(type)}
                        <span>{formatEventType(type)}</span>
                      </div>
                      <Badge variant="outline" className="text-xs">
                        {count}
                      </Badge>
                    </div>
                    <Progress 
                      value={percentage} 
                      className="h-1"
                    />
                  </motion.div>
                );
              })}
            </CardContent>
          </Card>
        </motion.div>
      )}

      {/* Recent Activity */}
      {showRecentActivity && recentEvents.length > 0 && (
        <motion.div
          initial={prefersReducedMotion ? {} : { opacity: 0, y: 20 }}
          animate={prefersReducedMotion ? {} : { opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.2 }}
        >
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium flex items-center gap-2">
                <TrendingUpIcon size={16} />
                Recent Activity
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {recentEvents.map((event, index) => (
                <motion.div
                  key={event.id}
                  initial={prefersReducedMotion ? {} : { opacity: 0, x: -20 }}
                  animate={prefersReducedMotion ? {} : { opacity: 1, x: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.05 }}
                  className="flex items-start gap-3 p-2 rounded-md hover:bg-muted/50 transition-colors"
                >
                  {getEventIcon(event.type)}
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium truncate">
                        {formatEventType(event.type)}
                      </span>
                      <span className="text-xs text-muted-foreground">
                        {formatTimestamp(event.timestamp)}
                      </span>
                    </div>
                    
                    {event.payload.data && (
                      <p className="text-xs text-muted-foreground mt-1 truncate">
                        {typeof event.payload.data === 'string' 
                          ? event.payload.data 
                          : JSON.stringify(event.payload.data).slice(0, 50) + '...'
                        }
                      </p>
                    )}
                  </div>
                </motion.div>
              ))}
              
              {events.length > maxRecentEvents && (
                <div className="text-center pt-2">
                  <span className="text-xs text-muted-foreground">
                    +{events.length - maxRecentEvents} more events
                  </span>
                </div>
              )}
            </CardContent>
          </Card>
        </motion.div>
      )}

      {/* No Data State */}
      {!isConnected && events.length === 0 && (
        <motion.div
          initial={prefersReducedMotion ? {} : { opacity: 0, y: 20 }}
          animate={prefersReducedMotion ? {} : { opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <Card className="border-dashed">
            <CardContent className="p-8 text-center">
              <WifiOffIcon size={48} className="mx-auto text-muted-foreground mb-4" />
              <h3 className="font-medium mb-2">No Real-time Data</h3>
              <p className="text-sm text-muted-foreground">
                Connect to start receiving live updates and analytics.
              </p>
            </CardContent>
          </Card>
        </motion.div>
      )}
    </div>
  );
}
