import { io, Socket } from 'socket.io-client';

// WebSocket Event Types
export interface WebSocketEvent {
  type: string;
  payload: any;
  timestamp: Date;
  id: string;
}

// Real-time event types for Lighthouse
export type LighthouseEventType =
  | 'agent_status_update'
  | 'knowledge_item_added'
  | 'insight_generated'
  | 'learning_event'
  | 'project_update'
  | 'user_activity'
  | 'system_metric'
  | 'error_occurred'
  | 'recommendation_generated';

export interface LighthouseEvent extends WebSocketEvent {
  type: LighthouseEventType;
  payload: {
    projectId?: string;
    userId?: string;
    data: any;
    metadata?: Record<string, any>;
  };
}

// WebSocket connection states
export enum ConnectionState {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  RECONNECTING = 'reconnecting',
  ERROR = 'error',
}

// Event listener type
export type EventListener<T = any> = (event: LighthouseEvent & { payload: { data: T } }) => void;

// WebSocket service configuration
export interface WebSocketConfig {
  url: string;
  autoConnect?: boolean;
  reconnectAttempts?: number;
  reconnectDelay?: number;
  timeout?: number;
  auth?: {
    token?: string;
    userId?: string;
  };
}

// WebSocket service class
export class WebSocketService {
  private socket: Socket | null = null;
  private config: WebSocketConfig;
  private connectionState: ConnectionState = ConnectionState.DISCONNECTED;
  private eventListeners: Map<string, Set<EventListener>> = new Map();
  private reconnectAttempts = 0;
  private maxReconnectAttempts: number;
  private reconnectDelay: number;
  private heartbeatInterval: NodeJS.Timeout | null = null;

  constructor(config: WebSocketConfig) {
    this.config = {
      autoConnect: true,
      reconnectAttempts: 5,
      reconnectDelay: 1000,
      timeout: 10000,
      ...config,
    };
    this.maxReconnectAttempts = this.config.reconnectAttempts!;
    this.reconnectDelay = this.config.reconnectDelay!;

    if (this.config.autoConnect) {
      this.connect();
    }
  }

  // Connect to WebSocket server
  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.socket?.connected) {
        resolve();
        return;
      }

      this.setConnectionState(ConnectionState.CONNECTING);

      this.socket = io(this.config.url, {
        timeout: this.config.timeout,
        auth: this.config.auth,
        transports: ['websocket', 'polling'],
      });

      // Connection successful
      this.socket.on('connect', () => {
        this.setConnectionState(ConnectionState.CONNECTED);
        this.reconnectAttempts = 0;
        this.startHeartbeat();
        resolve();
      });

      // Connection error
      this.socket.on('connect_error', (error) => {
        console.error('WebSocket connection error:', error);
        this.setConnectionState(ConnectionState.ERROR);
        
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
          this.scheduleReconnect();
        } else {
          reject(new Error(`Failed to connect after ${this.maxReconnectAttempts} attempts`));
        }
      });

      // Disconnection
      this.socket.on('disconnect', (reason) => {
        console.log('WebSocket disconnected:', reason);
        this.setConnectionState(ConnectionState.DISCONNECTED);
        this.stopHeartbeat();

        if (reason === 'io server disconnect') {
          // Server initiated disconnect, don't reconnect automatically
          return;
        }

        if (this.reconnectAttempts < this.maxReconnectAttempts) {
          this.scheduleReconnect();
        }
      });

      // Handle incoming events
      this.socket.onAny((eventType: string, data: any) => {
        this.handleIncomingEvent(eventType, data);
      });

      // Heartbeat response
      this.socket.on('pong', () => {
        // Server is alive
      });
    });
  }

  // Disconnect from WebSocket server
  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
    this.setConnectionState(ConnectionState.DISCONNECTED);
    this.stopHeartbeat();
  }

  // Send event to server
  emit(eventType: LighthouseEventType, data: any, metadata?: Record<string, any>): void {
    if (!this.socket?.connected) {
      console.warn('Cannot emit event: WebSocket not connected');
      return;
    }

    const event: LighthouseEvent = {
      type: eventType,
      payload: {
        data,
        metadata,
      },
      timestamp: new Date(),
      id: crypto.randomUUID(),
    };

    this.socket.emit(eventType, event);
  }

  // Subscribe to events
  on<T = any>(eventType: LighthouseEventType, listener: EventListener<T>): () => void {
    if (!this.eventListeners.has(eventType)) {
      this.eventListeners.set(eventType, new Set());
    }
    
    this.eventListeners.get(eventType)!.add(listener as EventListener);

    // Return unsubscribe function
    return () => {
      this.off(eventType, listener);
    };
  }

  // Unsubscribe from events
  off<T = any>(eventType: LighthouseEventType, listener: EventListener<T>): void {
    const listeners = this.eventListeners.get(eventType);
    if (listeners) {
      listeners.delete(listener as EventListener);
      if (listeners.size === 0) {
        this.eventListeners.delete(eventType);
      }
    }
  }

  // Subscribe to events once
  once<T = any>(eventType: LighthouseEventType, listener: EventListener<T>): void {
    const onceListener: EventListener<T> = (event) => {
      listener(event);
      this.off(eventType, onceListener);
    };
    this.on(eventType, onceListener);
  }

  // Get connection state
  getConnectionState(): ConnectionState {
    return this.connectionState;
  }

  // Check if connected
  isConnected(): boolean {
    return this.connectionState === ConnectionState.CONNECTED && this.socket?.connected === true;
  }

  // Handle incoming events
  private handleIncomingEvent(eventType: string, data: any): void {
    const listeners = this.eventListeners.get(eventType as LighthouseEventType);
    if (listeners) {
      const event: LighthouseEvent = {
        type: eventType as LighthouseEventType,
        payload: data,
        timestamp: new Date(),
        id: data.id || crypto.randomUUID(),
      };

      listeners.forEach(listener => {
        try {
          listener(event);
        } catch (error) {
          console.error('Error in event listener:', error);
        }
      });
    }
  }

  // Set connection state and notify listeners
  private setConnectionState(state: ConnectionState): void {
    if (this.connectionState !== state) {
      this.connectionState = state;
      this.emit('system_metric', {
        type: 'connection_state_change',
        state,
        timestamp: new Date(),
      });
    }
  }

  // Schedule reconnection attempt
  private scheduleReconnect(): void {
    this.setConnectionState(ConnectionState.RECONNECTING);
    this.reconnectAttempts++;
    
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1); // Exponential backoff
    
    setTimeout(() => {
      if (this.connectionState === ConnectionState.RECONNECTING) {
        this.connect().catch(error => {
          console.error('Reconnection failed:', error);
        });
      }
    }, delay);
  }

  // Start heartbeat to keep connection alive
  private startHeartbeat(): void {
    this.stopHeartbeat();
    this.heartbeatInterval = setInterval(() => {
      if (this.socket?.connected) {
        this.socket.emit('ping');
      }
    }, 30000); // 30 seconds
  }

  // Stop heartbeat
  private stopHeartbeat(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
  }
}

// Default WebSocket service instance
let defaultWebSocketService: WebSocketService | null = null;

export function getWebSocketService(): WebSocketService {
  if (!defaultWebSocketService) {
    // In a real application, this would come from environment variables
    const config: WebSocketConfig = {
      url: process.env.NEXT_PUBLIC_WEBSOCKET_URL || 'ws://localhost:3001',
      autoConnect: true,
      reconnectAttempts: 5,
      reconnectDelay: 1000,
      timeout: 10000,
    };

    defaultWebSocketService = new WebSocketService(config);
  }
  return defaultWebSocketService;
}

export function setWebSocketService(service: WebSocketService): void {
  defaultWebSocketService = service;
}

// Utility functions for common event patterns
export const WebSocketUtils = {
  // Create a standardized event
  createEvent: (
    type: LighthouseEventType,
    data: any,
    metadata?: Record<string, any>
  ): LighthouseEvent => ({
    type,
    payload: { data, metadata },
    timestamp: new Date(),
    id: crypto.randomUUID(),
  }),

  // Check if event is recent (within last 5 minutes)
  isRecentEvent: (event: LighthouseEvent): boolean => {
    const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
    return event.timestamp > fiveMinutesAgo;
  },

  // Filter events by type
  filterEventsByType: (
    events: LighthouseEvent[],
    types: LighthouseEventType[]
  ): LighthouseEvent[] => {
    return events.filter(event => types.includes(event.type));
  },

  // Group events by type
  groupEventsByType: (events: LighthouseEvent[]): Record<string, LighthouseEvent[]> => {
    return events.reduce((groups, event) => {
      if (!groups[event.type]) {
        groups[event.type] = [];
      }
      groups[event.type].push(event);
      return groups;
    }, {} as Record<string, LighthouseEvent[]>);
  },
};
