import { useState, useCallback, useRef } from 'react';
import { getFileProcessor, ProcessedFile, SupportedFileType } from './FileProcessor';
import { useAIErrorHandling } from '../error/useAIErrorHandling';
import { useLighthouseStore } from '../store/lighthouse-store';

// File processing state
interface FileProcessingState {
  isProcessing: boolean;
  processedFiles: ProcessedFile[];
  processingProgress: Record<string, number>;
  errors: Record<string, string>;
  totalFiles: number;
  completedFiles: number;
}

// File processing hook
export function useFileProcessing() {
  const [state, setState] = useState<FileProcessingState>({
    isProcessing: false,
    processedFiles: [],
    processingProgress: {},
    errors: {},
    totalFiles: 0,
    completedFiles: 0,
  });

  const { handleFileProcessingError } = useAIErrorHandling();
  const { addKnowledgeItem, recordLearning } = useLighthouseStore();
  const fileProcessor = getFileProcessor();
  const abortControllerRef = useRef<AbortController | null>(null);

  // Process single file
  const processFile = useCallback(async (file: File): Promise<ProcessedFile | null> => {
    const fileId = `${file.name}-${file.lastModified}`;
    
    try {
      setState(prev => ({
        ...prev,
        processingProgress: { ...prev.processingProgress, [fileId]: 0 },
      }));

      // Simulate progress updates
      const progressInterval = setInterval(() => {
        setState(prev => ({
          ...prev,
          processingProgress: {
            ...prev.processingProgress,
            [fileId]: Math.min((prev.processingProgress[fileId] || 0) + 10, 90)
          },
        }));
      }, 200);

      const processedFile = await fileProcessor.processFile(file);

      clearInterval(progressInterval);

      setState(prev => ({
        ...prev,
        processingProgress: { ...prev.processingProgress, [fileId]: 100 },
        processedFiles: [...prev.processedFiles, processedFile],
        completedFiles: prev.completedFiles + 1,
      }));

      // Add to knowledge base if processing was successful
      if (!processedFile.error && processedFile.content) {
        addKnowledgeItem({
          id: processedFile.id,
          type: 'document',
          content: processedFile.content,
          domain: 'general', // Could be inferred from content
          metadata: {
            filename: file.name,
            fileType: file.type,
            size: file.size,
            processingTime: processedFile.metadata.processingTime,
            summary: processedFile.metadata.summary,
            keyPoints: processedFile.metadata.keyPoints,
            concepts: processedFile.metadata.concepts,
            confidence: processedFile.metadata.confidence,
            credibility: processedFile.metadata.confidence,
          },
        });

        // Record learning event
        recordLearning({
          type: 'knowledge_acquisition',
          trigger: 'file_upload',
          before: {
            confidence: 0.5,
            understanding: 'File uploaded for processing',
            relatedNodes: [],
            supportingEvidence: []
          },
          after: {
            confidence: processedFile.metadata.confidence,
            understanding: `Processed ${file.name}: ${processedFile.metadata.summary}`,
            relatedNodes: processedFile.metadata.concepts || [],
            supportingEvidence: processedFile.metadata.keyPoints || []
          }
        });
      }

      return processedFile;
    } catch (error) {
      const errorId = handleFileProcessingError(error, file.name, {
        component: 'File Processor',
        retryAction: () => processFile(file),
      });

      setState(prev => ({
        ...prev,
        errors: { ...prev.errors, [fileId]: errorId },
        completedFiles: prev.completedFiles + 1,
      }));

      return null;
    }
  }, [fileProcessor, handleFileProcessingError, addKnowledgeItem, recordLearning]);

  // Process multiple files
  const processFiles = useCallback(async (files: File[]): Promise<ProcessedFile[]> => {
    if (state.isProcessing) {
      throw new Error('File processing already in progress');
    }

    setState(prev => ({
      ...prev,
      isProcessing: true,
      totalFiles: files.length,
      completedFiles: 0,
      processedFiles: [],
      processingProgress: {},
      errors: {},
    }));

    abortControllerRef.current = new AbortController();
    const results: ProcessedFile[] = [];

    try {
      // Process files in batches to avoid overwhelming the system
      const batchSize = 3;
      for (let i = 0; i < files.length; i += batchSize) {
        if (abortControllerRef.current.signal.aborted) {
          break;
        }

        const batch = files.slice(i, i + batchSize);
        const batchResults = await Promise.allSettled(
          batch.map(file => processFile(file))
        );

        for (const result of batchResults) {
          if (result.status === 'fulfilled' && result.value) {
            results.push(result.value);
          }
        }
      }

      return results;
    } finally {
      setState(prev => ({ ...prev, isProcessing: false }));
      abortControllerRef.current = null;
    }
  }, [state.isProcessing, processFile]);

  // Cancel processing
  const cancelProcessing = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    setState(prev => ({ ...prev, isProcessing: false }));
  }, []);

  // Clear processed files
  const clearProcessedFiles = useCallback(() => {
    setState(prev => ({
      ...prev,
      processedFiles: [],
      processingProgress: {},
      errors: {},
      totalFiles: 0,
      completedFiles: 0,
    }));
  }, []);

  // Get supported file types
  const getSupportedTypes = useCallback((): SupportedFileType[] => {
    return fileProcessor.getSupportedTypes();
  }, [fileProcessor]);

  // Check if file is supported
  const isFileSupported = useCallback((file: File): boolean => {
    return fileProcessor.getProcessor(file) !== null;
  }, [fileProcessor]);

  // Get processing progress percentage
  const getOverallProgress = useCallback((): number => {
    if (state.totalFiles === 0) return 0;
    return Math.round((state.completedFiles / state.totalFiles) * 100);
  }, [state.totalFiles, state.completedFiles]);

  // Get file processing status
  const getFileStatus = useCallback((file: File): 'pending' | 'processing' | 'completed' | 'error' => {
    const fileId = `${file.name}-${file.lastModified}`;
    
    if (state.errors[fileId]) return 'error';
    if (state.processedFiles.some(pf => pf.originalFile.name === file.name)) return 'completed';
    if (state.processingProgress[fileId] !== undefined) return 'processing';
    return 'pending';
  }, [state.errors, state.processedFiles, state.processingProgress]);

  return {
    // State
    isProcessing: state.isProcessing,
    processedFiles: state.processedFiles,
    totalFiles: state.totalFiles,
    completedFiles: state.completedFiles,
    
    // Actions
    processFile,
    processFiles,
    cancelProcessing,
    clearProcessedFiles,
    
    // Utilities
    getSupportedTypes,
    isFileSupported,
    getOverallProgress,
    getFileStatus,
    
    // Progress tracking
    processingProgress: state.processingProgress,
    errors: state.errors,
  };
}

// File validation utilities
export const FileValidationUtils = {
  validateFileSize: (file: File, maxSizeMB: number = 50): boolean => {
    return file.size <= maxSizeMB * 1024 * 1024;
  },

  validateFileType: (file: File, allowedTypes: SupportedFileType[]): boolean => {
    return allowedTypes.includes(file.type as SupportedFileType);
  },

  validateFileName: (file: File): boolean => {
    // Check for valid filename (no special characters that could cause issues)
    const validNamePattern = /^[a-zA-Z0-9._\-\s]+$/;
    return validNamePattern.test(file.name) && file.name.length <= 255;
  },

  getFileExtension: (file: File): string => {
    return file.name.split('.').pop()?.toLowerCase() || '';
  },

  formatFileSize: (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  },

  getMimeTypeFromExtension: (extension: string): SupportedFileType | null => {
    const mimeMap: Record<string, SupportedFileType> = {
      'txt': 'text/plain',
      'md': 'text/markdown',
      'pdf': 'application/pdf',
      'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'doc': 'application/msword',
      'csv': 'text/csv',
      'json': 'application/json',
      'html': 'text/html',
      'jpg': 'image/jpeg',
      'jpeg': 'image/jpeg',
      'png': 'image/png',
      'webp': 'image/webp',
      'mp3': 'audio/mpeg',
      'wav': 'audio/wav',
      'webm': 'audio/webm',
      'mp4': 'video/mp4',
    };
    
    return mimeMap[extension.toLowerCase()] || null;
  },
};
