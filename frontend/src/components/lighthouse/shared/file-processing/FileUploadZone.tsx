import React, { useCallback, useState, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '~/lib/utils';
import { Button } from '~/components/ui/button';
import { Progress } from '~/components/ui/progress';
import { Badge } from '~/components/ui/badge';
import { Card, CardContent } from '~/components/ui/card';
import { useFileProcessing, FileValidationUtils } from './useFileProcessing';
import { AccessibilityUtils } from '~/utils/accessibilityUtils';
import { UploadIcon, FileTextIcon, XIcon, CheckIcon, AlertTriangleIcon } from '../icons';

interface FileUploadZoneProps {
  onFilesProcessed?: (files: any[]) => void;
  maxFiles?: number;
  maxSizeMB?: number;
  acceptedTypes?: string[];
  className?: string;
  disabled?: boolean;
}

export function FileUploadZone({
  onFilesProcessed,
  maxFiles = 10,
  maxSizeMB = 50,
  acceptedTypes,
  className,
  disabled = false,
}: FileUploadZoneProps) {
  const [isDragOver, setIsDragOver] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const prefersReducedMotion = AccessibilityUtils.prefersReducedMotion();

  const {
    isProcessing,
    processedFiles,
    processFiles,
    cancelProcessing,
    clearProcessedFiles,
    getSupportedTypes,
    isFileSupported,
    getOverallProgress,
    getFileStatus,
    processingProgress,
  } = useFileProcessing();

  const supportedTypes = acceptedTypes || getSupportedTypes();

  // Handle file selection
  const handleFileSelect = useCallback((files: FileList | null) => {
    if (!files || disabled) return;

    const fileArray = Array.from(files);
    const validFiles: File[] = [];
    const errors: string[] = [];

    for (const file of fileArray) {
      // Validate file count
      if (validFiles.length >= maxFiles) {
        errors.push(`Maximum ${maxFiles} files allowed`);
        break;
      }

      // Validate file size
      if (!FileValidationUtils.validateFileSize(file, maxSizeMB)) {
        errors.push(`${file.name}: File too large (max ${maxSizeMB}MB)`);
        continue;
      }

      // Validate file type
      if (!isFileSupported(file)) {
        errors.push(`${file.name}: Unsupported file type`);
        continue;
      }

      // Validate file name
      if (!FileValidationUtils.validateFileName(file)) {
        errors.push(`${file.name}: Invalid file name`);
        continue;
      }

      validFiles.push(file);
    }

    if (errors.length > 0) {
      console.warn('File validation errors:', errors);
      // You could show these errors in a toast or error state
    }

    setSelectedFiles(prev => [...prev, ...validFiles].slice(0, maxFiles));
  }, [disabled, maxFiles, maxSizeMB, isFileSupported]);

  // Handle drag and drop
  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    if (!disabled) {
      setIsDragOver(true);
    }
  }, [disabled]);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    if (!disabled) {
      handleFileSelect(e.dataTransfer.files);
    }
  }, [disabled, handleFileSelect]);

  // Handle file input change
  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    handleFileSelect(e.target.files);
  }, [handleFileSelect]);

  // Process selected files
  const handleProcessFiles = useCallback(async () => {
    if (selectedFiles.length === 0) return;

    try {
      const results = await processFiles(selectedFiles);
      onFilesProcessed?.(results);
    } catch (error) {
      console.error('File processing error:', error);
    }
  }, [selectedFiles, processFiles, onFilesProcessed]);

  // Remove file from selection
  const removeFile = useCallback((index: number) => {
    setSelectedFiles(prev => prev.filter((_, i) => i !== index));
  }, []);

  // Clear all files
  const clearFiles = useCallback(() => {
    setSelectedFiles([]);
    clearProcessedFiles();
  }, [clearProcessedFiles]);

  const getFileIcon = (file: File) => {
    if (file.type.startsWith('image/')) return '🖼️';
    if (file.type.startsWith('audio/')) return '🎵';
    if (file.type.startsWith('video/')) return '🎥';
    if (file.type === 'application/pdf') return '📄';
    return '📝';
  };

  const getStatusIcon = (file: File) => {
    const status = getFileStatus(file);
    switch (status) {
      case 'completed':
        return <CheckIcon size={16} className="text-green-500" />;
      case 'error':
        return <AlertTriangleIcon size={16} className="text-red-500" />;
      case 'processing':
        return <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />;
      default:
        return <FileTextIcon size={16} className="text-muted-foreground" />;
    }
  };

  return (
    <div className={cn('space-y-4', className)}>
      {/* Drop Zone */}
      <motion.div
        className={cn(
          'relative border-2 border-dashed rounded-lg p-8 text-center transition-all duration-200',
          isDragOver
            ? 'border-primary bg-primary/5 scale-105'
            : 'border-muted-foreground/25 hover:border-muted-foreground/50',
          disabled && 'opacity-50 cursor-not-allowed',
          'focus-within:ring-2 focus-within:ring-primary/20'
        )}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        animate={prefersReducedMotion ? {} : {
          scale: isDragOver ? 1.02 : 1,
          borderColor: isDragOver ? 'rgb(59 130 246)' : 'rgb(156 163 175 / 0.25)'
        }}
        transition={{ duration: 0.2 }}
      >
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept={supportedTypes.join(',')}
          onChange={handleInputChange}
          className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
          disabled={disabled}
          aria-label="Upload files"
        />

        <div className="space-y-4">
          <motion.div
            animate={prefersReducedMotion ? {} : {
              y: isDragOver ? -5 : 0,
              scale: isDragOver ? 1.1 : 1
            }}
            transition={{ duration: 0.2 }}
          >
            <UploadIcon size={48} className="mx-auto text-muted-foreground" />
          </motion.div>

          <div className="space-y-2">
            <h3 className="text-lg font-medium">
              {isDragOver ? 'Drop files here' : 'Upload files'}
            </h3>
            <p className="text-sm text-muted-foreground">
              Drag and drop files here, or click to select files
            </p>
            <p className="text-xs text-muted-foreground">
              Supports: {supportedTypes.slice(0, 3).join(', ')}
              {supportedTypes.length > 3 && ` and ${supportedTypes.length - 3} more`}
            </p>
            <p className="text-xs text-muted-foreground">
              Max {maxFiles} files, {maxSizeMB}MB each
            </p>
          </div>

          <Button
            variant="outline"
            onClick={() => fileInputRef.current?.click()}
            disabled={disabled}
            className="mt-4"
          >
            Select Files
          </Button>
        </div>
      </motion.div>

      {/* Selected Files */}
      <AnimatePresence>
        {selectedFiles.length > 0 && (
          <motion.div
            initial={prefersReducedMotion ? {} : { opacity: 0, y: 20 }}
            animate={prefersReducedMotion ? {} : { opacity: 1, y: 0 }}
            exit={prefersReducedMotion ? {} : { opacity: 0, y: -20 }}
            className="space-y-3"
          >
            <div className="flex items-center justify-between">
              <h4 className="font-medium">Selected Files ({selectedFiles.length})</h4>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={clearFiles}
                  disabled={isProcessing}
                >
                  Clear All
                </Button>
                <Button
                  onClick={handleProcessFiles}
                  disabled={isProcessing || selectedFiles.length === 0}
                  size="sm"
                >
                  {isProcessing ? 'Processing...' : 'Process Files'}
                </Button>
              </div>
            </div>

            {/* Processing Progress */}
            {isProcessing && (
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>Processing files...</span>
                  <span>{getOverallProgress()}%</span>
                </div>
                <Progress value={getOverallProgress()} className="h-2" />
                <Button
                  variant="outline"
                  size="sm"
                  onClick={cancelProcessing}
                  className="w-full"
                >
                  Cancel Processing
                </Button>
              </div>
            )}

            {/* File List */}
            <div className="space-y-2 max-h-60 overflow-y-auto">
              {selectedFiles.map((file, index) => {
                const fileId = `${file.name}-${file.lastModified}`;
                const progress = processingProgress[fileId] || 0;
                const status = getFileStatus(file);

                return (
                  <motion.div
                    key={fileId}
                    initial={prefersReducedMotion ? {} : { opacity: 0, x: -20 }}
                    animate={prefersReducedMotion ? {} : { opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                  >
                    <Card className="p-3">
                      <div className="flex items-center gap-3">
                        <span className="text-2xl">{getFileIcon(file)}</span>
                        
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2">
                            <p className="text-sm font-medium truncate">{file.name}</p>
                            {getStatusIcon(file)}
                          </div>
                          <p className="text-xs text-muted-foreground">
                            {FileValidationUtils.formatFileSize(file.size)}
                          </p>
                          
                          {status === 'processing' && (
                            <div className="mt-2">
                              <Progress value={progress} className="h-1" />
                            </div>
                          )}
                        </div>

                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeFile(index)}
                          disabled={isProcessing}
                          className="h-8 w-8 p-0"
                        >
                          <XIcon size={14} />
                        </Button>
                      </div>
                    </Card>
                  </motion.div>
                );
              })}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Processed Files Summary */}
      {processedFiles.length > 0 && (
        <motion.div
          initial={prefersReducedMotion ? {} : { opacity: 0, y: 20 }}
          animate={prefersReducedMotion ? {} : { opacity: 1, y: 0 }}
          className="p-4 bg-green-50 dark:bg-green-950 rounded-lg border border-green-200 dark:border-green-800"
        >
          <div className="flex items-center gap-2">
            <CheckIcon size={20} className="text-green-600" />
            <span className="font-medium text-green-800 dark:text-green-200">
              Successfully processed {processedFiles.length} files
            </span>
          </div>
          <p className="text-sm text-green-600 dark:text-green-300 mt-1">
            Files have been added to your knowledge base and are ready for analysis.
          </p>
        </motion.div>
      )}
    </div>
  );
}
