// File Processing System for Lighthouse
import { getAIService } from '../ai';

// Supported file types
export type SupportedFileType = 
  | 'text/plain'
  | 'text/markdown'
  | 'application/pdf'
  | 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
  | 'application/msword'
  | 'text/csv'
  | 'application/json'
  | 'text/html'
  | 'image/jpeg'
  | 'image/png'
  | 'image/webp'
  | 'audio/mpeg'
  | 'audio/wav'
  | 'audio/webm'
  | 'video/mp4'
  | 'video/webm';

// File processing result
export interface ProcessedFile {
  id: string;
  originalFile: File;
  type: SupportedFileType;
  content: string;
  metadata: {
    size: number;
    lastModified: Date;
    processingTime: number;
    extractedText?: string;
    summary?: string;
    keyPoints?: string[];
    concepts?: string[];
    language?: string;
    confidence: number;
  };
  chunks?: TextChunk[];
  embeddings?: number[][];
  error?: string;
}

// Text chunk for large documents
export interface TextChunk {
  id: string;
  content: string;
  startIndex: number;
  endIndex: number;
  metadata: {
    chunkIndex: number;
    totalChunks: number;
    wordCount: number;
    embedding?: number[];
  };
}

// File processor interface
export interface IFileProcessor {
  supportedTypes: SupportedFileType[];
  canProcess(file: File): boolean;
  process(file: File): Promise<ProcessedFile>;
}

// Text file processor
export class TextFileProcessor implements IFileProcessor {
  supportedTypes: SupportedFileType[] = [
    'text/plain',
    'text/markdown',
    'text/csv',
    'application/json',
    'text/html'
  ];

  canProcess(file: File): boolean {
    return this.supportedTypes.includes(file.type as SupportedFileType);
  }

  async process(file: File): Promise<ProcessedFile> {
    const startTime = Date.now();
    
    try {
      const content = await this.readFileAsText(file);
      const aiService = getAIService();
      
      // Analyze content with AI
      const analysis = await aiService.analyzeContent(content, 'text');
      
      // Create chunks for large files
      const chunks = content.length > 5000 ? this.createTextChunks(content) : undefined;
      
      // Generate embeddings for chunks or full content
      const embeddings = chunks 
        ? await Promise.all(chunks.map(chunk => aiService.embedText(chunk.content)))
        : [await aiService.embedText(content)];

      return {
        id: crypto.randomUUID(),
        originalFile: file,
        type: file.type as SupportedFileType,
        content,
        metadata: {
          size: file.size,
          lastModified: new Date(file.lastModified),
          processingTime: Date.now() - startTime,
          extractedText: content,
          summary: analysis.summary,
          keyPoints: analysis.keyPoints,
          concepts: analysis.concepts,
          language: this.detectLanguage(content),
          confidence: 0.95,
        },
        chunks,
        embeddings,
      };
    } catch (error) {
      return {
        id: crypto.randomUUID(),
        originalFile: file,
        type: file.type as SupportedFileType,
        content: '',
        metadata: {
          size: file.size,
          lastModified: new Date(file.lastModified),
          processingTime: Date.now() - startTime,
          confidence: 0,
        },
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  private async readFileAsText(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = () => reject(new Error('Failed to read file'));
      reader.readAsText(file);
    });
  }

  private createTextChunks(content: string, chunkSize: number = 1000): TextChunk[] {
    const chunks: TextChunk[] = [];
    const words = content.split(/\s+/);
    
    for (let i = 0; i < words.length; i += chunkSize) {
      const chunkWords = words.slice(i, i + chunkSize);
      const chunkContent = chunkWords.join(' ');
      const startIndex = content.indexOf(chunkWords[0]);
      const endIndex = startIndex + chunkContent.length;
      
      chunks.push({
        id: crypto.randomUUID(),
        content: chunkContent,
        startIndex,
        endIndex,
        metadata: {
          chunkIndex: Math.floor(i / chunkSize),
          totalChunks: Math.ceil(words.length / chunkSize),
          wordCount: chunkWords.length,
        },
      });
    }
    
    return chunks;
  }

  private detectLanguage(content: string): string {
    // Simple language detection based on common words
    const englishWords = ['the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'];
    const words = content.toLowerCase().split(/\s+/).slice(0, 100);
    const englishCount = words.filter(word => englishWords.includes(word)).length;
    
    return englishCount > words.length * 0.1 ? 'en' : 'unknown';
  }
}

// PDF file processor (simplified - would need pdf-lib or similar in production)
export class PDFFileProcessor implements IFileProcessor {
  supportedTypes: SupportedFileType[] = ['application/pdf'];

  canProcess(file: File): boolean {
    return this.supportedTypes.includes(file.type as SupportedFileType);
  }

  async process(file: File): Promise<ProcessedFile> {
    const startTime = Date.now();
    
    try {
      // In a real implementation, you would use a PDF parsing library
      // For now, we'll return a placeholder
      const content = `[PDF Content] - ${file.name}\nThis is a placeholder for PDF content extraction.`;
      
      const aiService = getAIService();
      const analysis = await aiService.analyzeContent(content, 'document');

      return {
        id: crypto.randomUUID(),
        originalFile: file,
        type: file.type as SupportedFileType,
        content,
        metadata: {
          size: file.size,
          lastModified: new Date(file.lastModified),
          processingTime: Date.now() - startTime,
          extractedText: content,
          summary: analysis.summary,
          keyPoints: analysis.keyPoints,
          concepts: analysis.concepts,
          confidence: 0.7, // Lower confidence for placeholder
        },
        embeddings: [await aiService.embedText(content)],
      };
    } catch (error) {
      return {
        id: crypto.randomUUID(),
        originalFile: file,
        type: file.type as SupportedFileType,
        content: '',
        metadata: {
          size: file.size,
          lastModified: new Date(file.lastModified),
          processingTime: Date.now() - startTime,
          confidence: 0,
        },
        error: error instanceof Error ? error.message : 'PDF processing failed',
      };
    }
  }
}

// Image file processor
export class ImageFileProcessor implements IFileProcessor {
  supportedTypes: SupportedFileType[] = [
    'image/jpeg',
    'image/png',
    'image/webp'
  ];

  canProcess(file: File): boolean {
    return this.supportedTypes.includes(file.type as SupportedFileType);
  }

  async process(file: File): Promise<ProcessedFile> {
    const startTime = Date.now();
    
    try {
      const imageUrl = await this.fileToDataURL(file);
      
      // In a real implementation, you would use OCR or image analysis
      const content = `[Image] - ${file.name}\nImage analysis would be performed here.`;
      
      const aiService = getAIService();
      const analysis = await aiService.analyzeContent(content, 'text');

      return {
        id: crypto.randomUUID(),
        originalFile: file,
        type: file.type as SupportedFileType,
        content,
        metadata: {
          size: file.size,
          lastModified: new Date(file.lastModified),
          processingTime: Date.now() - startTime,
          extractedText: content,
          summary: analysis.summary,
          keyPoints: analysis.keyPoints,
          concepts: analysis.concepts,
          confidence: 0.6, // Lower confidence for placeholder
        },
        embeddings: [await aiService.embedText(content)],
      };
    } catch (error) {
      return {
        id: crypto.randomUUID(),
        originalFile: file,
        type: file.type as SupportedFileType,
        content: '',
        metadata: {
          size: file.size,
          lastModified: new Date(file.lastModified),
          processingTime: Date.now() - startTime,
          confidence: 0,
        },
        error: error instanceof Error ? error.message : 'Image processing failed',
      };
    }
  }

  private fileToDataURL(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = () => reject(new Error('Failed to read image file'));
      reader.readAsDataURL(file);
    });
  }
}

// Main file processor factory
export class FileProcessorFactory {
  private processors: IFileProcessor[] = [
    new TextFileProcessor(),
    new PDFFileProcessor(),
    new ImageFileProcessor(),
  ];

  getProcessor(file: File): IFileProcessor | null {
    return this.processors.find(processor => processor.canProcess(file)) || null;
  }

  getSupportedTypes(): SupportedFileType[] {
    return this.processors.flatMap(processor => processor.supportedTypes);
  }

  async processFile(file: File): Promise<ProcessedFile> {
    const processor = this.getProcessor(file);
    
    if (!processor) {
      throw new Error(`Unsupported file type: ${file.type}`);
    }

    return processor.process(file);
  }

  async processFiles(files: File[]): Promise<ProcessedFile[]> {
    const results = await Promise.allSettled(
      files.map(file => this.processFile(file))
    );

    return results.map((result, index) => {
      if (result.status === 'fulfilled') {
        return result.value;
      } else {
        // Return error result for failed processing
        return {
          id: crypto.randomUUID(),
          originalFile: files[index],
          type: files[index].type as SupportedFileType,
          content: '',
          metadata: {
            size: files[index].size,
            lastModified: new Date(files[index].lastModified),
            processingTime: 0,
            confidence: 0,
          },
          error: result.reason instanceof Error ? result.reason.message : 'Processing failed',
        };
      }
    });
  }
}

// Default file processor instance
let defaultFileProcessor: FileProcessorFactory | null = null;

export function getFileProcessor(): FileProcessorFactory {
  if (!defaultFileProcessor) {
    defaultFileProcessor = new FileProcessorFactory();
  }
  return defaultFileProcessor;
}
