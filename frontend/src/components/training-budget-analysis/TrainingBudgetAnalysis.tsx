import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '~/components/ui/tabs';
import { Badge } from '~/components/ui/badge';
import { Button } from '~/components/ui/button';
import { Progress } from '~/components/ui/progress';
import { Separator } from '~/components/ui/separator';
import { 
  TrendingUp, 
  TrendingDown, 
  AlertTriangle, 
  Download, 
  Filter,
  DollarSign,
  BarChart3,
  PieChart,
  Calendar,
  Target
} from 'lucide-react';
import { BudgetOverviewCard } from './components/BudgetOverviewCard';
import { ActualVsPlanChart } from './components/ActualVsPlanChart';
import { BudgetBreakdownTable } from './components/BudgetBreakdownTable';
import { VarianceAnalysis } from './components/VarianceAnalysis';
import { BudgetTrendChart } from './components/BudgetTrendChart';
import { BudgetFilters } from './components/BudgetFilters';
import { ExportControls } from './components/ExportControls';
import { BudgetAlerts } from './components/BudgetAlerts';
import { useBudgetData } from './hooks/useBudgetData';
import { useBudgetFilters } from './hooks/useBudgetFilters';
import type { FilterOptions } from './types';

interface TrainingBudgetAnalysisProps {
  className?: string;
}

const TrainingBudgetAnalysis: React.FC<TrainingBudgetAnalysisProps> = ({ className }) => {
  const [activeTab, setActiveTab] = useState('overview');
  const [showFilters, setShowFilters] = useState(false);
  const [showExportModal, setShowExportModal] = useState(false);
  
  const { filters, updateFilters, resetFilters } = useBudgetFilters();
  const { budgetData, metrics, alerts, isLoading, error } = useBudgetData(filters);

  const budgetUtilizationColor = useMemo(() => {
    if (!metrics) return 'bg-gray-500';
    if (metrics.budgetUtilization > 100) return 'bg-red-500';
    if (metrics.budgetUtilization > 90) return 'bg-yellow-500';
    return 'bg-green-500';
  }, [metrics]);

  const varianceIcon = useMemo(() => {
    if (!metrics) return null;
    if (metrics.averageVariance > 0) return <TrendingUp className="h-4 w-4 text-red-500" />;
    if (metrics.averageVariance < 0) return <TrendingDown className="h-4 w-4 text-green-500" />;
    return <Target className="h-4 w-4 text-blue-500" />;
  }, [metrics]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (error) {
    return (
      <Card className="border-red-200 bg-red-50">
        <CardContent className="p-6">
          <div className="flex items-center space-x-2 text-red-600">
            <AlertTriangle className="h-5 w-5" />
            <span>Error loading budget data: {error}</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={`space-y-6 p-6 ${className}`}>
      {/* Header Section */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
        <div>
          <h1 className="text-3xl font-bold tracking-tight bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            Training Budget Analysis
          </h1>
          <p className="text-muted-foreground mt-2">
            Comprehensive analysis of actual vs planned training budget with real-time insights
          </p>
        </div>
        
        <div className="flex items-center space-x-3">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center space-x-2"
          >
            <Filter className="h-4 w-4" />
            <span>Filters</span>
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowExportModal(true)}
            className="flex items-center space-x-2"
          >
            <Download className="h-4 w-4" />
            <span>Export</span>
          </Button>
        </div>
      </div>

      {/* Alerts Section */}
      {alerts && alerts.length > 0 && (
        <BudgetAlerts alerts={alerts} />
      )}

      {/* Key Metrics Cards */}
      {metrics && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="border-l-4 border-l-blue-500">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Budget Utilization</p>
                  <p className="text-2xl font-bold">{metrics.budgetUtilization.toFixed(1)}%</p>
                </div>
                <div className={`p-3 rounded-full ${budgetUtilizationColor}`}>
                  <DollarSign className="h-6 w-6 text-white" />
                </div>
              </div>
              <Progress value={metrics.budgetUtilization} className="mt-3" />
            </CardContent>
          </Card>

          <Card className="border-l-4 border-l-green-500">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Average Variance</p>
                  <p className="text-2xl font-bold flex items-center space-x-2">
                    <span>{metrics.averageVariance > 0 ? '+' : ''}{metrics.averageVariance.toFixed(1)}%</span>
                    {varianceIcon}
                  </p>
                </div>
                <div className="p-3 rounded-full bg-green-500">
                  <BarChart3 className="h-6 w-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-l-4 border-l-yellow-500">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Categories Over Budget</p>
                  <p className="text-2xl font-bold">{metrics.categoriesOverBudget}</p>
                </div>
                <div className="p-3 rounded-full bg-yellow-500">
                  <AlertTriangle className="h-6 w-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-l-4 border-l-purple-500">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Risk Score</p>
                  <p className="text-2xl font-bold">{metrics.riskScore}/100</p>
                </div>
                <div className="p-3 rounded-full bg-purple-500">
                  <PieChart className="h-6 w-6 text-white" />
                </div>
              </div>
              <div className="mt-3">
                <Badge variant={metrics.riskScore > 70 ? 'destructive' : metrics.riskScore > 40 ? 'default' : 'secondary'}>
                  {metrics.riskScore > 70 ? 'High Risk' : metrics.riskScore > 40 ? 'Medium Risk' : 'Low Risk'}
                </Badge>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filters Panel */}
      {showFilters && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Filter className="h-5 w-5" />
              <span>Filters</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <BudgetFilters
              filters={filters}
              onFiltersChange={updateFilters}
              onReset={resetFilters}
            />
          </CardContent>
        </Card>
      )}

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview" className="flex items-center space-x-2">
            <BarChart3 className="h-4 w-4" />
            <span>Overview</span>
          </TabsTrigger>
          <TabsTrigger value="charts" className="flex items-center space-x-2">
            <PieChart className="h-4 w-4" />
            <span>Charts</span>
          </TabsTrigger>
          <TabsTrigger value="breakdown" className="flex items-center space-x-2">
            <DollarSign className="h-4 w-4" />
            <span>Breakdown</span>
          </TabsTrigger>
          <TabsTrigger value="variance" className="flex items-center space-x-2">
            <TrendingUp className="h-4 w-4" />
            <span>Variance</span>
          </TabsTrigger>
          <TabsTrigger value="trends" className="flex items-center space-x-2">
            <Calendar className="h-4 w-4" />
            <span>Trends</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {budgetData && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <BudgetOverviewCard data={budgetData} />
              <ActualVsPlanChart data={budgetData} />
            </div>
          )}
        </TabsContent>

        <TabsContent value="charts" className="space-y-6">
          {budgetData && (
            <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
              <ActualVsPlanChart data={budgetData} showDetailed />
              <BudgetTrendChart data={budgetData} />
            </div>
          )}
        </TabsContent>

        <TabsContent value="breakdown" className="space-y-6">
          {budgetData && (
            <BudgetBreakdownTable data={budgetData} />
          )}
        </TabsContent>

        <TabsContent value="variance" className="space-y-6">
          {budgetData && (
            <VarianceAnalysis data={budgetData} />
          )}
        </TabsContent>

        <TabsContent value="trends" className="space-y-6">
          {budgetData && (
            <BudgetTrendChart data={budgetData} showDetailed />
          )}
        </TabsContent>
      </Tabs>

      {/* Export Modal */}
      {showExportModal && (
        <ExportControls
          isOpen={showExportModal}
          onClose={() => setShowExportModal(false)}
          data={budgetData}
          filters={filters}
        />
      )}
    </div>
  );
};

export default TrainingBudgetAnalysis;