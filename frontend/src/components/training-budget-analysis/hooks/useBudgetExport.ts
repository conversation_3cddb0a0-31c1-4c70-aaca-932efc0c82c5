import { useState, useCallback } from 'react';
import type { BudgetData, ExportOptions } from '../types';

/**
 * Hook for handling budget data export functionality
 */
export const useBudgetExport = () => {
  const [isExporting, setIsExporting] = useState(false);
  const [exportProgress, setExportProgress] = useState(0);

  const exportData = useCallback(async (
    data: BudgetData,
    options: ExportOptions
  ): Promise<void> => {
    setIsExporting(true);
    setExportProgress(0);

    try {
      // Simulate export progress
      const progressInterval = setInterval(() => {
        setExportProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return prev;
          }
          return prev + 10;
        });
      }, 200);

      // Generate export based on format
      switch (options.format) {
        case 'csv':
          await exportToCSV(data, options);
          break;
        case 'excel':
          await exportToExcel(data, options);
          break;
        case 'pdf':
          await exportToPDF(data, options);
          break;
        case 'json':
          await exportToJSON(data, options);
          break;
        default:
          throw new Error(`Unsupported export format: ${options.format}`);
      }

      clearInterval(progressInterval);
      setExportProgress(100);
      
      // Reset after a short delay
      setTimeout(() => {
        setExportProgress(0);
        setIsExporting(false);
      }, 1000);
    } catch (error) {
      console.error('Export failed:', error);
      setIsExporting(false);
      setExportProgress(0);
      throw error;
    }
  }, []);

  const exportToCSV = async (data: BudgetData, options: ExportOptions): Promise<void> => {
    const headers = [
      'Category',
      'Subcategory',
      'Planned Amount',
      'Actual Amount',
      'Variance',
      'Variance %',
      'Status',
      'Department',
      'Period',
      'Description'
    ];

    const rows = data.items.map(item => [
      item.category,
      item.subcategory || '',
      item.plannedAmount.toString(),
      item.actualAmount.toString(),
      item.variance.toString(),
      item.variancePercentage.toString(),
      item.status,
      item.department || '',
      item.period,
      item.description || ''
    ]);

    const csvContent = [headers, ...rows]
      .map(row => row.map(field => `"${field}"`).join(','))
      .join('\n');

    downloadFile(csvContent, 'budget-analysis.csv', 'text/csv');
  };

  const exportToExcel = async (data: BudgetData, options: ExportOptions): Promise<void> => {
    // For now, export as CSV (in a real implementation, you'd use a library like xlsx)
    await exportToCSV(data, options);
  };

  const exportToPDF = async (data: BudgetData, options: ExportOptions): Promise<void> => {
    // For now, create a simple text representation
    // In a real implementation, you'd use a library like jsPDF
    const content = `
Budget Analysis Report
===================

Total Planned: $${data.totalPlanned.toLocaleString()}
Total Actual: $${data.totalActual.toLocaleString()}
Total Variance: $${data.totalVariance.toLocaleString()}
Variance %: ${data.totalVariancePercentage.toFixed(2)}%
Utilization Rate: ${data.utilizationRate.toFixed(2)}%

Detailed Breakdown:
${data.items.map(item => 
  `${item.category}: Planned $${item.plannedAmount.toLocaleString()}, Actual $${item.actualAmount.toLocaleString()}, Variance: ${item.variancePercentage.toFixed(2)}%`
).join('\n')}
`;

    downloadFile(content, 'budget-analysis.txt', 'text/plain');
  };

  const exportToJSON = async (data: BudgetData, options: ExportOptions): Promise<void> => {
    const jsonContent = JSON.stringify(data, null, 2);
    downloadFile(jsonContent, 'budget-analysis.json', 'application/json');
  };

  const downloadFile = (content: string, filename: string, mimeType: string): void => {
    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  return {
    exportData,
    isExporting,
    exportProgress
  };
};