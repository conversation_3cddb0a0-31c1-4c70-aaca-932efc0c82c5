import React, { useState, useEffect, useMemo } from 'react';
import type { BudgetData, BudgetItem, DashboardMetrics, ChartDataPoint } from '../types';

// Mock data generator for demonstration
const generateMockBudgetData = (): BudgetData => {
  const departments = ['Engineering', 'Marketing', 'Sales', 'HR', 'Operations', 'Finance'];
  const categories = [
    'Technical Training',
    'Leadership Development',
    'Compliance Training',
    'Soft Skills',
    'Certification Programs',
    'Conference Attendance',
    'Online Courses',
    'Workshop Series',
    'Mentoring Programs',
    'External Training'
  ];

  const items: BudgetItem[] = [];
  
  for (let i = 0; i < 25; i++) {
    const plannedAmount = Math.floor(Math.random() * 50000) + 10000;
    const actualAmount = plannedAmount * (0.4 + Math.random() * 0.8); // 40% to 120% of planned
    const variance = actualAmount - plannedAmount;
    const variancePercentage = (variance / plannedAmount) * 100;
    
    let status: 'over' | 'under' | 'on-track';
    if (Math.abs(variancePercentage) <= 5) {
      status = 'on-track';
    } else if (variancePercentage > 0) {
      status = 'over';
    } else {
      status = 'under';
    }

    items.push({
      id: `budget-${i + 1}`,
      category: categories[Math.floor(Math.random() * categories.length)],
      department: departments[Math.floor(Math.random() * departments.length)],
      plannedAmount,
      actualAmount: Math.round(actualAmount),
      variance: Math.round(variance),
      variancePercentage: Math.round(variancePercentage * 100) / 100,
      status,
      period: `Q${Math.floor(Math.random() * 4) + 1} 2024`,
      lastUpdated: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000),
      quarter: `Q${Math.floor(Math.random() * 4) + 1}`,
      year: 2024
    });
  }

  const totalPlanned = items.reduce((sum, item) => sum + item.plannedAmount, 0);
  const totalActual = items.reduce((sum, item) => sum + item.actualAmount, 0);
  const totalVariance = totalActual - totalPlanned;
  const totalVariancePercentage = (totalVariance / totalPlanned) * 100;
  const utilizationRate = (totalActual / totalPlanned) * 100;

  return {
    items,
    totalPlanned,
    totalActual,
    totalVariance,
    totalVariancePercentage,
    utilizationRate,
    lastUpdated: new Date()
  };
};

// Generate trend data for charts
const generateTrendData = (): ChartDataPoint[] => {
  const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
  return months.map(month => {
    const planned = Math.floor(Math.random() * 100000) + 50000;
    const actual = Math.floor(Math.random() * 100000) + 40000;
    return {
      period: month,
      planned,
      actual,
      variance: actual - planned
    };
  });
};

export const useBudgetData = () => {
  const [data, setData] = useState<BudgetData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);

  // Simulate API call
  const fetchBudgetData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // In a real app, this would be an API call
      const budgetData = generateMockBudgetData();
      setData(budgetData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch budget data');
    } finally {
      setLoading(false);
    }
  };

  // Refresh data
  const refreshData = async () => {
    try {
      setRefreshing(true);
      setError(null);
      
      await new Promise(resolve => setTimeout(resolve, 500));
      const budgetData = generateMockBudgetData();
      setData(budgetData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to refresh budget data');
    } finally {
      setRefreshing(false);
    }
  };

  // Calculate dashboard metrics
  const metrics = useMemo((): DashboardMetrics | null => {
    if (!data) return null;

    const overBudgetItems = data.items.filter(item => item.status === 'over');
    const underBudgetItems = data.items.filter(item => item.status === 'under');
    const onTrackItems = data.items.filter(item => item.status === 'on-track');

    const avgVariance = data.items.reduce((sum, item) => sum + Math.abs(item.variancePercentage), 0) / data.items.length;
    
    const departmentBreakdown = data.items.reduce((acc, item) => {
      if (!item.department) return acc;
      if (!acc[item.department]) {
        acc[item.department] = { planned: 0, actual: 0, variance: 0 };
      }
      acc[item.department].planned += item.plannedAmount;
      acc[item.department].actual += item.actualAmount;
      acc[item.department].variance += item.variance;
      return acc;
    }, {} as Record<string, { planned: number; actual: number; variance: number }>);

    const categoryBreakdown = data.items.reduce((acc, item) => {
      if (!acc[item.category]) {
        acc[item.category] = { planned: 0, actual: 0, variance: 0 };
      }
      acc[item.category].planned += item.plannedAmount;
      acc[item.category].actual += item.actualAmount;
      acc[item.category].variance += item.variance;
      return acc;
    }, {} as Record<string, { planned: number; actual: number; variance: number }>);

    // Find top performers and concerns
    const sortedByVariance = [...data.items].sort((a, b) => b.variancePercentage - a.variancePercentage);
    const topConcerns = sortedByVariance.filter(item => item.status === 'over').slice(0, 5);
    const topPerformers = sortedByVariance.filter(item => item.status === 'under').slice(-5).reverse();

    return {
      totalCategories: data.items.length,
      categoriesOverBudget: overBudgetItems.length,
      budgetUtilization: (data.totalActual / data.totalPlanned) * 100,
      averageVariance: Math.round(avgVariance * 100) / 100,
      totalSavings: Math.max(0, data.totalPlanned - data.totalActual),
      riskScore: Math.min(100, Math.max(0, (overBudgetItems.length / data.items.length) * 100)),
      trendData: generateTrendData()
    };
  }, [data]);

  // Get filtered data based on criteria
  const getFilteredData = useMemo(() => {
    return (filters: {
      department?: string;
      category?: string;
      status?: string;
      dateRange?: { start: Date; end: Date };
      amountRange?: { min: number; max: number };
    }) => {
      if (!data) return [];

      return data.items.filter(item => {
        if (filters.department && item.department !== filters.department) return false;
        if (filters.category && item.category !== filters.category) return false;
        if (filters.status && item.status !== filters.status) return false;
        
        if (filters.amountRange) {
          if (item.actualAmount < filters.amountRange.min || item.actualAmount > filters.amountRange.max) {
            return false;
          }
        }
        
        if (filters.dateRange) {
          const itemDate = new Date(item.lastUpdated);
          if (itemDate < filters.dateRange.start || itemDate > filters.dateRange.end) {
            return false;
          }
        }
        
        return true;
      });
    };
  }, [data]);

  // Get unique values for filters
  const filterOptions = useMemo(() => {
    if (!data) return { departments: [], categories: [], statuses: [] };

    const departments = [...new Set(data.items.map(item => item.department).filter(Boolean))];
    const categories = [...new Set(data.items.map(item => item.category))];
    const statuses = [...new Set(data.items.map(item => item.status))];

    return {
      departments: departments.sort(),
      categories: categories.sort(),
      statuses: statuses.sort()
    };
  }, [data]);

  // Export data functionality
  const exportData = (format: 'csv' | 'json' | 'pdf' | 'excel', options?: {
    includeSummary?: boolean;
    includeVarianceAnalysis?: boolean;
    includeChartData?: boolean;
    includeTrendData?: boolean;
    customFields?: string[];
  }) => {
    if (!data) return null;

    const exportData = {
      summary: options?.includeSummary ? {
        totalPlanned: data.totalPlanned,
        totalActual: data.totalActual,
        totalVariance: data.totalVariance,
        utilizationRate: data.utilizationRate
      } : undefined,
      items: data.items,
      varianceAnalysis: options?.includeVarianceAnalysis ? metrics : undefined,
      trendData: options?.includeTrendData ? metrics?.trendData : undefined,
      exportedAt: new Date().toISOString()
    };

    switch (format) {
      case 'json':
        return JSON.stringify(exportData, null, 2);
      case 'csv':
        // Simple CSV export for items
        const headers = ['Category', 'Department', 'Planned', 'Actual', 'Variance', 'Variance %', 'Status'];
        const rows = data.items.map(item => [
          item.category,
          item.department || '',
          item.plannedAmount,
          item.actualAmount,
          item.variance,
          item.variancePercentage,
          item.status
        ]);
        return [headers, ...rows].map(row => row.join(',')).join('\n');
      default:
        return exportData;
    }
  };

  // Initialize data on mount
  useEffect(() => {
    fetchBudgetData();
  }, []);

  return {
    data,
    metrics,
    loading,
    error,
    refreshing,
    refreshData,
    getFilteredData,
    filterOptions,
    exportData,
    refetch: fetchBudgetData
  };
};