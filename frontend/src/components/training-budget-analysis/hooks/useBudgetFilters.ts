import React, { useState, useCallback, useMemo } from 'react';
import type { FilterOptions, BudgetItem } from '../types';

interface FilterState extends FilterOptions {
  // Additional state properties if needed
}

const initialFilterState: FilterOptions = {
  searchTerm: '',
  departments: [],
  categories: [],
  status: [],
  statuses: [],
  dateRange: {
    start: new Date(),
    end: new Date()
  },
  amountRange: {
    min: 0,
    max: 1000000
  },
  sortBy: 'category',
  sortOrder: 'asc'
};

export const useBudgetFilters = (data: BudgetItem[] = []) => {
  const [filters, setFilters] = useState<FilterOptions>(initialFilterState);
  const [activeFiltersCount, setActiveFiltersCount] = useState(0);

  // Update search term
  const updateSearchTerm = useCallback((term: string) => {
    setFilters(prev => ({ ...prev, searchTerm: term }));
  }, []);

  // Update department filter
  const updateDepartment = useCallback((department: string) => {
    setFilters(prev => ({ ...prev, department }));
  }, []);

  // Update category filter
  const updateCategory = useCallback((category: string) => {
    setFilters(prev => ({ ...prev, category }));
  }, []);

  // Update status filter
  const updateStatus = useCallback((status: ('under' | 'over' | 'on-track' | 'at-risk')[]) => {
    setFilters(prev => ({ ...prev, status }));
  }, []);

  // Update date range
  const updateDateRange = useCallback((start: Date, end: Date) => {
    setFilters(prev => ({
      ...prev,
      dateRange: { start, end }
    }));
  }, []);

  // Update amount range
  const updateAmountRange = useCallback((min: number, max: number) => {
    setFilters(prev => ({
      ...prev,
      amountRange: { min, max }
    }));
  }, []);

  // Update sorting
  const updateSort = useCallback((sortBy: FilterState['sortBy'], sortOrder?: 'asc' | 'desc') => {
    setFilters(prev => ({
      ...prev,
      sortBy,
      sortOrder: sortOrder || (prev.sortBy === sortBy && prev.sortOrder === 'asc' ? 'desc' : 'asc')
    }));
  }, []);

  // Clear all filters
  const clearFilters = useCallback(() => {
    setFilters(initialFilterState);
  }, []);

  // Clear specific filter
  const clearFilter = useCallback((filterKey: keyof FilterState) => {
    setFilters(prev => ({
      ...prev,
      [filterKey]: initialFilterState[filterKey]
    }));
  }, []);

  // Get filter options from data
  const filterOptions = useMemo((): FilterOptions => {
    const departments = [...new Set(data.map(item => item.department).filter((dept): dept is string => Boolean(dept)))].sort();
    const categories = [...new Set(data.map(item => item.category))].sort();
    const statuses = [...new Set(data.map(item => item.status))].sort();
    
    // Calculate amount range from data
    const amounts = data.map(item => item.actualAmount);
    const minAmount = amounts.length > 0 ? Math.min(...amounts) : 0;
    const maxAmount = amounts.length > 0 ? Math.max(...amounts) : 1000000;
    
    // Calculate date range from data
    const dates = data.map(item => new Date(item.lastUpdated));
    const minDate = dates.length > 0 ? new Date(Math.min(...dates.map(d => d.getTime()))) : new Date();
    const maxDate = dates.length > 0 ? new Date(Math.max(...dates.map(d => d.getTime()))) : new Date();

    return {
      departments,
      categories,
      statuses,
      amountRange: { min: minAmount, max: maxAmount },
      dateRange: { start: minDate, end: maxDate }
    };
  }, [data]);

  // Apply filters to data
  const filteredData = useMemo(() => {
    let filtered = [...data];

    // Apply search term filter
    if (filters.searchTerm) {
      const searchLower = filters.searchTerm.toLowerCase();
      filtered = filtered.filter(item => 
        item.category.toLowerCase().includes(searchLower) ||
        (item.department && item.department.toLowerCase().includes(searchLower))
      );
    }

    // Apply department filter
    if (filters.departments.length > 0) {
      filtered = filtered.filter(item => item.department && filters.departments.includes(item.department));
    }

    // Apply category filter
    if (filters.categories.length > 0) {
      filtered = filtered.filter(item => filters.categories.includes(item.category));
    }

    // Apply status filter
    if (filters.status.length > 0) {
      filtered = filtered.filter(item => filters.status.includes(item.status));
    }

    // Apply date range filter
    if (filters.dateRange.start && filters.dateRange.end) {
      filtered = filtered.filter(item => {
        const itemDate = new Date(item.lastUpdated);
        return itemDate >= filters.dateRange.start! && itemDate <= filters.dateRange.end!;
      });
    }

    // Apply amount range filter
    if (filters.amountRange.min > 0 || filters.amountRange.max < 1000000) {
      filtered = filtered.filter(item => 
        item.actualAmount >= filters.amountRange.min && 
        item.actualAmount <= filters.amountRange.max
      );
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue: any;
      let bValue: any;

      switch (filters.sortBy) {
        case 'category':
          aValue = a.category;
          bValue = b.category;
          break;
        case 'department':
          aValue = a.department || '';
          bValue = b.department || '';
          break;
        case 'planned':
          aValue = a.plannedAmount;
          bValue = b.plannedAmount;
          break;
        case 'actual':
          aValue = a.actualAmount;
          bValue = b.actualAmount;
          break;
        case 'variance':
          aValue = a.variance;
          bValue = b.variance;
          break;
        case 'variancePercentage':
          aValue = a.variancePercentage;
          bValue = b.variancePercentage;
          break;
        case 'lastUpdated':
          aValue = new Date(a.lastUpdated);
          bValue = new Date(b.lastUpdated);
          break;
        default:
          aValue = a.category;
          bValue = b.category;
      }

      if (typeof aValue === 'string' && typeof bValue === 'string') {
        const comparison = aValue.localeCompare(bValue);
        return filters.sortOrder === 'asc' ? comparison : -comparison;
      }

      if (aValue instanceof Date && bValue instanceof Date) {
        const comparison = aValue.getTime() - bValue.getTime();
        return filters.sortOrder === 'asc' ? comparison : -comparison;
      }

      const comparison = aValue - bValue;
      return filters.sortOrder === 'asc' ? comparison : -comparison;
    });

    return filtered;
  }, [data, filters]);

  // Calculate active filters count
  const calculateActiveFilters = useMemo(() => {
    let count = 0;
    
    if (filters.searchTerm) count++;
    if (filters.departments.length > 0) count++;
    if (filters.categories.length > 0) count++;
    if (filters.status.length > 0) count++;
    if (filters.dateRange.start && filters.dateRange.end) count++;
    if (filters.amountRange.min > 0 || filters.amountRange.max < 1000000) count++;
    
    setActiveFiltersCount(count);
    return count;
  }, [filters]);

  // Get active filter labels
  const activeFilterLabels = useMemo(() => {
    const labels: Array<{ key: string; label: string; value: string }> = [];
    
    if (filters.searchTerm) {
      labels.push({ key: 'searchTerm', label: 'Search', value: filters.searchTerm });
    }
    
    if (filters.departments.length > 0) {
      labels.push({ key: 'departments', label: 'Departments', value: filters.departments.join(', ') });
    }
    
    if (filters.categories.length > 0) {
      labels.push({ key: 'categories', label: 'Categories', value: filters.categories.join(', ') });
    }
    
    if (filters.status.length > 0) {
      labels.push({ key: 'status', label: 'Status', value: filters.status.join(', ') });
    }
    
    if (filters.dateRange.start && filters.dateRange.end) {
      const start = filters.dateRange.start.toLocaleDateString();
      const end = filters.dateRange.end.toLocaleDateString();
      labels.push({ key: 'dateRange', label: 'Date Range', value: `${start} - ${end}` });
    }
    
    if (filters.amountRange.min > 0 || filters.amountRange.max < 1000000) {
      const min = new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD', minimumFractionDigits: 0 }).format(filters.amountRange.min);
      const max = new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD', minimumFractionDigits: 0 }).format(filters.amountRange.max);
      labels.push({ key: 'amountRange', label: 'Amount Range', value: `${min} - ${max}` });
    }
    
    return labels;
  }, [filters]);

  // Get filter summary
  const filterSummary = useMemo(() => {
    const total = data.length;
    const filtered = filteredData.length;
    const percentage = total > 0 ? Math.round((filtered / total) * 100) : 0;
    
    return {
      total,
      filtered,
      percentage,
      hidden: total - filtered
    };
  }, [data.length, filteredData.length]);

  // Preset filters
  const applyPresetFilter = useCallback((preset: 'over-budget' | 'under-budget' | 'on-track' | 'high-variance' | 'recent') => {
    switch (preset) {
      case 'over-budget':
        setFilters(prev => ({ ...prev, status: ['over'] }));
        break;
      case 'under-budget':
        setFilters(prev => ({ ...prev, status: ['under'] }));
        break;
      case 'on-track':
        setFilters(prev => ({ ...prev, status: ['on-track'] }));
        break;
      case 'high-variance':
        setFilters(prev => ({ ...prev, sortBy: 'variancePercentage', sortOrder: 'desc' }));
        break;
      case 'recent':
        const oneWeekAgo = new Date();
        oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
        setFilters(prev => ({
          ...prev,
          dateRange: { start: oneWeekAgo, end: new Date() }
        }));
        break;
    }
  }, []);

  // Add updateFilters function for compatibility
  const updateFilters = useCallback((newFilters: FilterOptions) => {
    setFilters(newFilters);
  }, []);

  // Add resetFilters function for compatibility
  const resetFilters = useCallback(() => {
    setFilters(initialFilterState);
  }, []);

  return {
    filters,
    filteredData,
    filterOptions,
    activeFiltersCount: calculateActiveFilters,
    activeFilterLabels,
    filterSummary,
    updateSearchTerm,
    updateDepartment,
    updateCategory,
    updateStatus,
    updateDateRange,
    updateAmountRange,
    updateSort,
    clearFilters,
    clearFilter,
    applyPresetFilter,
    updateFilters,
    resetFilters
  };
};