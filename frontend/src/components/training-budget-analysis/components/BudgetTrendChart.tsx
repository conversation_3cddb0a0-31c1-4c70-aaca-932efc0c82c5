import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import { Badge } from '~/components/ui/badge';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '~/components/ui/tabs';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  AreaChart,
  Area,
  BarChart,
  Bar
} from 'recharts';
import { 
  TrendingUp, 
  TrendingDown, 
  Calendar,
  BarChart3,
  Activity,
  Maximize2
} from 'lucide-react';
import type { BudgetData } from '../types';

interface BudgetTrendChartProps {
  data: BudgetData;
  className?: string;
}

export const BudgetTrendChart: React.FC<BudgetTrendChartProps> = ({ data, className }) => {
  const [chartType, setChartType] = useState<'line' | 'area' | 'bar'>('line');
  const [timeRange, setTimeRange] = useState<'3m' | '6m' | '12m' | 'all'>('6m');
  const [isFullscreen, setIsFullscreen] = useState(false);

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  // Generate trend data (mock data for demonstration)
  const generateTrendData = () => {
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    const currentMonth = new Date().getMonth();
    
    const getMonthsToShow = () => {
      switch (timeRange) {
        case '3m': return 3;
        case '6m': return 6;
        case '12m': return 12;
        default: return 12;
      }
    };

    const monthsToShow = getMonthsToShow();
    const startMonth = Math.max(0, currentMonth - monthsToShow + 1);
    
    return months.slice(startMonth, currentMonth + 1).map((month, index) => {
      const baseAmount = data.totalPlanned / 12;
      const variance = (Math.random() - 0.5) * 0.3; // ±15% variance
      const seasonalFactor = 1 + Math.sin((startMonth + index) * Math.PI / 6) * 0.1;
      
      const planned = baseAmount * seasonalFactor;
      const actual = planned * (1 + variance);
      const cumulative = {
        planned: planned * (index + 1),
        actual: actual * (index + 1)
      };
      
      return {
        month,
        planned: Math.round(planned),
        actual: Math.round(actual),
        variance: Math.round(actual - planned),
        cumulativePlanned: Math.round(cumulative.planned),
        cumulativeActual: Math.round(cumulative.actual),
        utilizationRate: Math.round((actual / planned) * 100)
      };
    });
  };

  const trendData = generateTrendData();
  
  const getTrendAnalysis = () => {
    if (trendData.length < 2) return null;
    
    const latest = trendData[trendData.length - 1];
    const previous = trendData[trendData.length - 2];
    
    const monthlyChange = latest.actual - previous.actual;
    const monthlyChangePercent = ((monthlyChange / previous.actual) * 100);
    
    const avgUtilization = trendData.reduce((sum, item) => sum + item.utilizationRate, 0) / trendData.length;
    const trend = monthlyChange > 0 ? 'increasing' : 'decreasing';
    
    return {
      monthlyChange,
      monthlyChangePercent,
      avgUtilization,
      trend,
      latest
    };
  };

  const analysis = getTrendAnalysis();

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-4 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-semibold mb-2">{label}</p>
          {payload.map((entry: any, index: number) => (
            <div key={index} className="flex items-center space-x-2 mb-1">
              <div 
                className="w-3 h-3 rounded-full" 
                style={{ backgroundColor: entry.color }}
              />
              <span className="text-sm">
                {entry.name}: {formatCurrency(entry.value)}
              </span>
              {entry.dataKey === 'actual' && entry.payload.variance && (
                <Badge 
                  variant={entry.payload.variance > 0 ? "destructive" : "secondary"}
                  className="text-xs"
                >
                  {entry.payload.variance > 0 ? '+' : ''}{formatCurrency(entry.payload.variance)}
                </Badge>
              )}
            </div>
          ))}
        </div>
      );
    }
    return null;
  };

  const renderChart = () => {
    const commonProps = {
      data: trendData,
      margin: { top: 5, right: 30, left: 20, bottom: 5 }
    };

    switch (chartType) {
      case 'area':
        return (
          <AreaChart {...commonProps}>
            <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
            <XAxis dataKey="month" />
            <YAxis tickFormatter={formatCurrency} />
            <Tooltip content={<CustomTooltip />} />
            <Legend />
            <Area 
              type="monotone" 
              dataKey="planned" 
              stackId="1" 
              stroke="#3b82f6" 
              fill="#3b82f6" 
              fillOpacity={0.3}
              name="Planned"
            />
            <Area 
              type="monotone" 
              dataKey="actual" 
              stackId="2" 
              stroke="#ef4444" 
              fill="#ef4444" 
              fillOpacity={0.3}
              name="Actual"
            />
          </AreaChart>
        );
      
      case 'bar':
        return (
          <BarChart {...commonProps}>
            <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
            <XAxis dataKey="month" />
            <YAxis tickFormatter={formatCurrency} />
            <Tooltip content={<CustomTooltip />} />
            <Legend />
            <Bar dataKey="planned" fill="#3b82f6" name="Planned" />
            <Bar dataKey="actual" fill="#ef4444" name="Actual" />
          </BarChart>
        );
      
      default:
        return (
          <LineChart {...commonProps}>
            <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
            <XAxis dataKey="month" />
            <YAxis tickFormatter={formatCurrency} />
            <Tooltip content={<CustomTooltip />} />
            <Legend />
            <Line 
              type="monotone" 
              dataKey="planned" 
              stroke="#3b82f6" 
              strokeWidth={3}
              dot={{ fill: '#3b82f6', strokeWidth: 2, r: 4 }}
              name="Planned"
            />
            <Line 
              type="monotone" 
              dataKey="actual" 
              stroke="#ef4444" 
              strokeWidth={3}
              dot={{ fill: '#ef4444', strokeWidth: 2, r: 4 }}
              name="Actual"
            />
          </LineChart>
        );
    }
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Trend Summary Cards */}
      {analysis && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium">Latest Month</span>
              </div>
              <div className="mt-2">
                <div className="text-2xl font-bold">
                  {formatCurrency(analysis.latest.actual)}
                </div>
                <div className="text-sm text-muted-foreground">
                  vs {formatCurrency(analysis.latest.planned)} planned
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                {analysis.trend === 'increasing' ? (
                  <TrendingUp className="h-4 w-4 text-red-500" />
                ) : (
                  <TrendingDown className="h-4 w-4 text-green-500" />
                )}
                <span className="text-sm font-medium">Monthly Change</span>
              </div>
              <div className="mt-2">
                <div className={`text-2xl font-bold ${
                  analysis.monthlyChange > 0 ? 'text-red-600' : 'text-green-600'
                }`}>
                  {analysis.monthlyChange > 0 ? '+' : ''}{formatCurrency(analysis.monthlyChange)}
                </div>
                <div className="text-sm text-muted-foreground">
                  {analysis.monthlyChangePercent > 0 ? '+' : ''}{analysis.monthlyChangePercent.toFixed(1)}%
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Activity className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium">Avg Utilization</span>
              </div>
              <div className="mt-2">
                <div className={`text-2xl font-bold ${
                  analysis.avgUtilization > 100 ? 'text-red-600' : 
                  analysis.avgUtilization > 90 ? 'text-yellow-600' : 'text-green-600'
                }`}>
                  {analysis.avgUtilization.toFixed(1)}%
                </div>
                <div className="text-sm text-muted-foreground">
                  Budget utilization rate
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <BarChart3 className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium">Trend Status</span>
              </div>
              <div className="mt-2">
                <Badge 
                  variant={analysis.trend === 'increasing' ? "destructive" : "secondary"}
                  className="text-sm"
                >
                  {analysis.trend === 'increasing' ? 'Increasing' : 'Decreasing'}
                </Badge>
                <div className="text-sm text-muted-foreground mt-1">
                  Spending trend
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Main Chart */}
      <Card className={isFullscreen ? 'fixed inset-0 z-50 m-4' : ''}>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Budget Trend Analysis</CardTitle>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsFullscreen(!isFullscreen)}
              >
                <Maximize2 className="h-4 w-4" />
              </Button>
            </div>
          </div>
          
          <div className="flex flex-wrap items-center gap-4 mt-4">
            {/* Time Range Selector */}
            <div className="flex items-center space-x-2">
              <span className="text-sm font-medium">Time Range:</span>
              <div className="flex space-x-1">
                {[{ value: '3m', label: '3M' }, { value: '6m', label: '6M' }, { value: '12m', label: '12M' }].map((range) => (
                  <Button
                    key={range.value}
                    variant={timeRange === range.value ? "default" : "outline"}
                    size="sm"
                    onClick={() => setTimeRange(range.value as any)}
                  >
                    {range.label}
                  </Button>
                ))}
              </div>
            </div>

            {/* Chart Type Selector */}
            <div className="flex items-center space-x-2">
              <span className="text-sm font-medium">Chart Type:</span>
              <div className="flex space-x-1">
                {[{ value: 'line', label: 'Line' }, { value: 'area', label: 'Area' }, { value: 'bar', label: 'Bar' }].map((type) => (
                  <Button
                    key={type.value}
                    variant={chartType === type.value ? "default" : "outline"}
                    size="sm"
                    onClick={() => setChartType(type.value as any)}
                  >
                    {type.label}
                  </Button>
                ))}
              </div>
            </div>
          </div>
        </CardHeader>
        
        <CardContent>
          <div className={`${isFullscreen ? 'h-[calc(100vh-200px)]' : 'h-96'}`}>
            <ResponsiveContainer width="100%" height="100%">
              {renderChart()}
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>

      {/* Cumulative Analysis */}
      <Card>
        <CardHeader>
          <CardTitle>Cumulative Budget Analysis</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <AreaChart data={trendData}>
                <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
                <XAxis dataKey="month" />
                <YAxis tickFormatter={formatCurrency} />
                <Tooltip content={<CustomTooltip />} />
                <Legend />
                <Area 
                  type="monotone" 
                  dataKey="cumulativePlanned" 
                  stroke="#3b82f6" 
                  fill="#3b82f6" 
                  fillOpacity={0.3}
                  name="Cumulative Planned"
                />
                <Area 
                  type="monotone" 
                  dataKey="cumulativeActual" 
                  stroke="#ef4444" 
                  fill="#ef4444" 
                  fillOpacity={0.3}
                  name="Cumulative Actual"
                />
              </AreaChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};