import React, { useState } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import { Badge } from '~/components/ui/badge';
import { Alert, AlertDescription } from '~/components/ui/alert';
import { Separator } from '~/components/ui/separator';
import { 
  AlertTriangle, 
  AlertCircle, 
  Info, 
  CheckCircle, 
  X,
  Bell,
  TrendingUp,
  TrendingDown,
  Clock,
  DollarSign,
  Target,
  Zap
} from 'lucide-react';
import type { BudgetAlert, BudgetData } from '../types';

interface BudgetAlertsProps {
  data: BudgetData;
  className?: string;
}

export const BudgetAlerts: React.FC<BudgetAlertsProps> = ({ data, className }) => {
  const [dismissedAlerts, setDismissedAlerts] = useState<string[]>([]);
  const [showAll, setShowAll] = useState(false);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const generateAlerts = (): BudgetAlert[] => {
    const alerts: BudgetAlert[] = [];

    // Critical: Over budget by more than 20%
    const criticalOverBudget = data.items.filter(item => 
      item.variancePercentage > 20 && item.status === 'over'
    );
    if (criticalOverBudget.length > 0) {
      alerts.push({
        id: 'critical-over-budget',
        type: 'critical',
        title: 'Critical Budget Overrun',
        message: `${criticalOverBudget.length} categories are over budget by more than 20%. Immediate action required.`,
        category: criticalOverBudget[0].category,
        amount: criticalOverBudget.reduce((sum, item) => sum + Math.abs(item.variance), 0),
        timestamp: new Date().toISOString(),
        actionRequired: true
      });
    }

    // Warning: Approaching budget limit (90-100%)
    const approachingLimit = data.items.filter(item => 
      item.actualAmount / item.plannedAmount >= 0.9 && 
      item.actualAmount / item.plannedAmount <= 1.0
    );
    if (approachingLimit.length > 0) {
      alerts.push({
        id: 'approaching-limit',
        type: 'warning',
        title: 'Budget Limit Approaching',
        message: `${approachingLimit.length} categories are approaching their budget limits (90%+ utilized).`,
        category: approachingLimit[0].category,
        amount: approachingLimit.reduce((sum, item) => sum + item.actualAmount, 0),
        timestamp: new Date().toISOString(),
        actionRequired: true
      });
    }

    // Info: Significant under-budget (less than 50% utilized)
    const significantlyUnder = data.items.filter(item => 
      item.actualAmount / item.plannedAmount < 0.5
    );
    if (significantlyUnder.length > 0) {
      alerts.push({
        id: 'significantly-under',
        type: 'info',
        title: 'Underutilized Budget',
        message: `${significantlyUnder.length} categories have used less than 50% of their allocated budget.`,
        category: significantlyUnder[0].category,
        amount: significantlyUnder.reduce((sum, item) => sum + (item.plannedAmount - item.actualAmount), 0),
        timestamp: new Date().toISOString(),
        actionRequired: false
      });
    }

    // Success: On-track categories
    const onTrackCount = data.items.filter(item => item.status === 'on-track').length;
    if (onTrackCount > 0) {
      alerts.push({
        id: 'on-track',
        type: 'success',
        title: 'Budget Performance',
        message: `${onTrackCount} categories are performing within expected budget ranges.`,
        category: 'Overall',
        amount: 0,
        timestamp: new Date().toISOString(),
        actionRequired: false
      });
    }

    // Warning: High variance in specific departments
    const departments = [...new Set(data.items.map(item => item.department).filter(Boolean))];
    departments.forEach(dept => {
      const deptItems = data.items.filter(item => item.department === dept);
      const avgVariance = deptItems.reduce((sum, item) => sum + Math.abs(item.variancePercentage), 0) / deptItems.length;
      
      if (avgVariance > 15) {
        alerts.push({
          id: `dept-variance-${dept}`,
          type: 'warning',
          title: 'Department Budget Variance',
          message: `${dept} department shows high budget variance (${avgVariance.toFixed(1)}% average).`,
          category: dept || 'Unknown',
          amount: deptItems.reduce((sum, item) => sum + Math.abs(item.variance), 0),
          timestamp: new Date().toISOString(),
          actionRequired: true
        });
      }
    });

    // Critical: Total budget exceeded
    if (data.totalVariance > 0) {
      alerts.push({
        id: 'total-budget-exceeded',
        type: 'critical',
        title: 'Total Budget Exceeded',
        message: `Overall budget has been exceeded by ${formatCurrency(data.totalVariance)} (${data.totalVariancePercentage.toFixed(1)}%).`,
        category: 'Overall',
        amount: data.totalVariance,
        timestamp: new Date().toISOString(),
        actionRequired: true
      });
    }

    return alerts.filter(alert => !dismissedAlerts.includes(alert.id));
  };

  const alerts = generateAlerts();
  const displayedAlerts = showAll ? alerts : alerts.slice(0, 5);

  const dismissAlert = (alertId: string) => {
    setDismissedAlerts(prev => [...prev, alertId]);
  };

  const getAlertIcon = (type: BudgetAlert['type']) => {
    switch (type) {
      case 'critical': return <AlertTriangle className="h-4 w-4" />;
      case 'warning': return <AlertCircle className="h-4 w-4" />;
      case 'info': return <Info className="h-4 w-4" />;
      case 'success': return <CheckCircle className="h-4 w-4" />;
      default: return <Bell className="h-4 w-4" />;
    }
  };

  const getAlertColor = (type: BudgetAlert['type']) => {
    switch (type) {
      case 'critical': return 'border-red-200 bg-red-50 text-red-800';
      case 'warning': return 'border-yellow-200 bg-yellow-50 text-yellow-800';
      case 'info': return 'border-blue-200 bg-blue-50 text-blue-800';
      case 'success': return 'border-green-200 bg-green-50 text-green-800';
      default: return 'border-gray-200 bg-gray-50 text-gray-800';
    }
  };

  const getAlertBadgeVariant = (type: BudgetAlert['type']) => {
    switch (type) {
      case 'critical': return 'destructive';
      case 'warning': return 'secondary';
      case 'info': return 'outline';
      case 'success': return 'secondary';
      default: return 'outline';
    }
  };

  const getPriorityAlerts = () => {
    return alerts.filter(alert => alert.actionRequired && alert.type === 'critical');
  };

  const getAlertStats = () => {
    const critical = alerts.filter(alert => alert.type === 'critical').length;
    const warning = alerts.filter(alert => alert.type === 'warning').length;
    const info = alerts.filter(alert => alert.type === 'info').length;
    const success = alerts.filter(alert => alert.type === 'success').length;
    
    return { critical, warning, info, success };
  };

  const stats = getAlertStats();
  const priorityAlerts = getPriorityAlerts();

  if (alerts.length === 0) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="text-center py-8">
            <CheckCircle className="h-12 w-12 mx-auto mb-4 text-green-500" />
            <h3 className="text-lg font-semibold mb-2">All Clear!</h3>
            <p className="text-muted-foreground">
              No budget alerts at this time. All categories are performing within expected ranges.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Alert Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Bell className="h-5 w-5" />
            <span>Budget Alerts</span>
            <Badge variant="outline">{alerts.length}</Badge>
          </CardTitle>
        </CardHeader>
        
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">{stats.critical}</div>
              <div className="text-sm text-muted-foreground">Critical</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-yellow-600">{stats.warning}</div>
              <div className="text-sm text-muted-foreground">Warning</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{stats.info}</div>
              <div className="text-sm text-muted-foreground">Info</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{stats.success}</div>
              <div className="text-sm text-muted-foreground">Success</div>
            </div>
          </div>

          {/* Priority Alerts */}
          {priorityAlerts.length > 0 && (
            <>
              <div className="mb-4">
                <h4 className="font-semibold text-red-600 mb-2 flex items-center space-x-2">
                  <Zap className="h-4 w-4" />
                  <span>Immediate Action Required</span>
                </h4>
                <div className="space-y-2">
                  {priorityAlerts.map((alert) => (
                    <Alert key={alert.id} className={getAlertColor(alert.type)}>
                      <div className="flex items-start justify-between">
                        <div className="flex items-start space-x-2">
                          {getAlertIcon(alert.type)}
                          <div>
                            <AlertDescription className="font-medium">
                              {alert.title}
                            </AlertDescription>
                            <AlertDescription className="mt-1">
                              {alert.message}
                            </AlertDescription>
                            {alert.amount > 0 && (
                              <div className="mt-2 flex items-center space-x-2">
                                <DollarSign className="h-3 w-3" />
                                <span className="text-sm font-medium">
                                  {formatCurrency(alert.amount)}
                                </span>
                              </div>
                            )}
                          </div>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => dismissAlert(alert.id)}
                          className="h-6 w-6 p-0"
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    </Alert>
                  ))}
                </div>
              </div>
              <Separator />
            </>
          )}

          {/* All Alerts */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <h4 className="font-semibold">All Alerts</h4>
              {alerts.length > 5 && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowAll(!showAll)}
                >
                  {showAll ? 'Show Less' : `Show All (${alerts.length})`}
                </Button>
              )}
            </div>
            
            {displayedAlerts.map((alert) => (
              <div key={alert.id} className={`p-4 rounded-lg border ${getAlertColor(alert.type)}`}>
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-3">
                    <div className="mt-0.5">
                      {getAlertIcon(alert.type)}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        <h5 className="font-semibold">{alert.title}</h5>
                        <Badge variant={getAlertBadgeVariant(alert.type)} className="text-xs">
                          {alert.type.toUpperCase()}
                        </Badge>
                        {alert.actionRequired && (
                          <Badge variant="destructive" className="text-xs">
                            ACTION REQUIRED
                          </Badge>
                        )}
                      </div>
                      <p className="text-sm mb-2">{alert.message}</p>
                      
                      <div className="flex items-center space-x-4 text-xs text-muted-foreground">
                        <div className="flex items-center space-x-1">
                          <Target className="h-3 w-3" />
                          <span>{alert.category}</span>
                        </div>
                        {alert.amount > 0 && (
                          <div className="flex items-center space-x-1">
                            <DollarSign className="h-3 w-3" />
                            <span>{formatCurrency(alert.amount)}</span>
                          </div>
                        )}
                        <div className="flex items-center space-x-1">
                          <Clock className="h-3 w-3" />
                          <span>{new Date(alert.timestamp).toLocaleTimeString()}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => dismissAlert(alert.id)}
                    className="h-6 w-6 p-0 opacity-70 hover:opacity-100"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
            <Button variant="outline" size="sm" className="justify-start">
              <TrendingUp className="h-4 w-4 mr-2" />
              Review Over-Budget Items
            </Button>
            <Button variant="outline" size="sm" className="justify-start">
              <TrendingDown className="h-4 w-4 mr-2" />
              Reallocate Unused Budget
            </Button>
            <Button variant="outline" size="sm" className="justify-start">
              <Bell className="h-4 w-4 mr-2" />
              Configure Alert Settings
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};