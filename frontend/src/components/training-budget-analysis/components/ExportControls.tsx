import React, { useState } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import { Badge } from '~/components/ui/badge';
import { Checkbox } from '~/components/ui/checkbox';
import { Label } from '~/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '~/components/ui/dialog';
import { Separator } from '~/components/ui/separator';
import { 
  Download, 
  FileText, 
  FileSpreadsheet, 
  Image, 
  Mail,
  Calendar,
  Settings,
  CheckCircle
} from 'lucide-react';
import type { BudgetData, ExportOptions } from '../types';

interface ExportControlsProps {
  data: BudgetData;
  className?: string;
}

export const ExportControls: React.FC<ExportControlsProps> = ({ data, className }) => {
  const [exportOptions, setExportOptions] = useState<ExportOptions>({
    format: 'csv',
    includeCharts: true,
    includeSummary: true,
    includeVarianceAnalysis: true,
    includeTrendData: false,
    dateRange: 'all',
    customFields: []
  });
  const [isExporting, setIsExporting] = useState(false);
  const [exportSuccess, setExportSuccess] = useState(false);

  const handleExportOptionChange = (key: keyof ExportOptions, value: any) => {
    setExportOptions(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleCustomFieldToggle = (field: string, checked: boolean) => {
    const currentFields = exportOptions.customFields || [];
    const newFields = checked 
      ? [...currentFields, field]
      : currentFields.filter(f => f !== field);
    
    handleExportOptionChange('customFields', newFields);
  };

  const generateCSV = () => {
    const headers = [
      'Category',
      'Department', 
      'Planned Amount',
      'Actual Amount',
      'Variance',
      'Variance %',
      'Status',
      'Last Updated'
    ];

    const rows = data.items.map(item => [
      item.category,
      item.department || '',
      item.plannedAmount.toString(),
      item.actualAmount.toString(),
      item.variance.toString(),
      item.variancePercentage.toFixed(2),
      item.status,
      item.lastUpdated || ''
    ]);

    const csvContent = [headers, ...rows]
      .map(row => row.map(cell => `"${cell}"`).join(','))
      .join('\n');

    return csvContent;
  };

  const generateJSON = () => {
    const exportData = {
      metadata: {
        exportDate: new Date().toISOString(),
        totalPlanned: data.totalPlanned,
        totalActual: data.totalActual,
        totalVariance: data.totalVariance,
        totalVariancePercentage: data.totalVariancePercentage,
        itemCount: data.items.length
      },
      budgetItems: data.items,
      ...(exportOptions.includeSummary && {
        summary: {
          overBudgetItems: data.items.filter(item => item.status === 'over').length,
          underBudgetItems: data.items.filter(item => item.status === 'under').length,
          onTrackItems: data.items.filter(item => item.status === 'on-track').length
        }
      })
    };

    return JSON.stringify(exportData, null, 2);
  };

  const downloadFile = (content: string, filename: string, mimeType: string) => {
    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  const handleExport = async () => {
    setIsExporting(true);
    setExportSuccess(false);

    try {
      const timestamp = new Date().toISOString().split('T')[0];
      
      switch (exportOptions.format) {
        case 'csv':
          const csvContent = generateCSV();
          downloadFile(csvContent, `training-budget-analysis-${timestamp}.csv`, 'text/csv');
          break;
          
        case 'json':
          const jsonContent = generateJSON();
          downloadFile(jsonContent, `training-budget-analysis-${timestamp}.json`, 'application/json');
          break;
          
        case 'pdf':
          // In a real implementation, you would use a PDF library like jsPDF
          console.log('PDF export would be implemented here');
          break;
          
        case 'excel':
          // In a real implementation, you would use a library like xlsx
          console.log('Excel export would be implemented here');
          break;
      }
      
      setExportSuccess(true);
      setTimeout(() => setExportSuccess(false), 3000);
    } catch (error) {
      console.error('Export failed:', error);
    } finally {
      setIsExporting(false);
    }
  };

  const getFormatIcon = (format: string) => {
    switch (format) {
      case 'csv': return <FileSpreadsheet className="h-4 w-4" />;
      case 'json': return <FileText className="h-4 w-4" />;
      case 'pdf': return <FileText className="h-4 w-4" />;
      case 'excel': return <FileSpreadsheet className="h-4 w-4" />;
      default: return <Download className="h-4 w-4" />;
    }
  };

  const getEstimatedFileSize = () => {
    const itemCount = data.items.length;
    const baseSize = itemCount * 100; // rough estimate in bytes
    
    switch (exportOptions.format) {
      case 'csv': return `~${Math.round(baseSize / 1024)}KB`;
      case 'json': return `~${Math.round(baseSize * 1.5 / 1024)}KB`;
      case 'pdf': return `~${Math.round(baseSize * 3 / 1024)}KB`;
      case 'excel': return `~${Math.round(baseSize * 2 / 1024)}KB`;
      default: return 'Unknown';
    }
  };

  const availableFields = [
    'description',
    'createdDate',
    'lastUpdated',
    'approvedBy',
    'notes',
    'tags'
  ];

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Download className="h-5 w-5" />
          <span>Export Data</span>
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Quick Export Buttons */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              setExportOptions(prev => ({ ...prev, format: 'csv' }));
              handleExport();
            }}
            className="flex items-center space-x-2"
          >
            <FileSpreadsheet className="h-4 w-4" />
            <span>CSV</span>
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              setExportOptions(prev => ({ ...prev, format: 'json' }));
              handleExport();
            }}
            className="flex items-center space-x-2"
          >
            <FileText className="h-4 w-4" />
            <span>JSON</span>
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              setExportOptions(prev => ({ ...prev, format: 'pdf' }));
              handleExport();
            }}
            className="flex items-center space-x-2"
          >
            <FileText className="h-4 w-4" />
            <span>PDF</span>
          </Button>
          
          <Dialog>
            <DialogTrigger asChild>
              <Button
                variant="outline"
                size="sm"
                className="flex items-center space-x-2"
              >
                <Settings className="h-4 w-4" />
                <span>Custom</span>
              </Button>
            </DialogTrigger>
            
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>Custom Export Options</DialogTitle>
                <DialogDescription>
                  Configure your export settings and download the data in your preferred format.
                </DialogDescription>
              </DialogHeader>
              
              <div className="space-y-4">
                {/* Format Selection */}
                <div className="space-y-2">
                  <Label>Export Format</Label>
                  <Select 
                    value={exportOptions.format} 
                    onValueChange={(value) => handleExportOptionChange('format', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="csv">
                        <div className="flex items-center space-x-2">
                          <FileSpreadsheet className="h-4 w-4" />
                          <span>CSV (Comma Separated)</span>
                        </div>
                      </SelectItem>
                      <SelectItem value="json">
                        <div className="flex items-center space-x-2">
                          <FileText className="h-4 w-4" />
                          <span>JSON (JavaScript Object)</span>
                        </div>
                      </SelectItem>
                      <SelectItem value="pdf">
                        <div className="flex items-center space-x-2">
                          <FileText className="h-4 w-4" />
                          <span>PDF (Portable Document)</span>
                        </div>
                      </SelectItem>
                      <SelectItem value="excel">
                        <div className="flex items-center space-x-2">
                          <FileSpreadsheet className="h-4 w-4" />
                          <span>Excel (Spreadsheet)</span>
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <Separator />

                {/* Include Options */}
                <div className="space-y-3">
                  <Label>Include in Export</Label>
                  
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="include-summary"
                        checked={exportOptions.includeSummary}
                        onCheckedChange={(checked) => 
                          handleExportOptionChange('includeSummary', checked)
                        }
                      />
                      <Label htmlFor="include-summary" className="text-sm font-normal">
                        Summary Statistics
                      </Label>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="include-variance"
                        checked={exportOptions.includeVarianceAnalysis}
                        onCheckedChange={(checked) => 
                          handleExportOptionChange('includeVarianceAnalysis', checked)
                        }
                      />
                      <Label htmlFor="include-variance" className="text-sm font-normal">
                        Variance Analysis
                      </Label>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="include-charts"
                        checked={exportOptions.includeCharts}
                        onCheckedChange={(checked) => 
                          handleExportOptionChange('includeCharts', checked)
                        }
                      />
                      <Label htmlFor="include-charts" className="text-sm font-normal">
                        Chart Data
                      </Label>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="include-trends"
                        checked={exportOptions.includeTrendData}
                        onCheckedChange={(checked) => 
                          handleExportOptionChange('includeTrendData', checked)
                        }
                      />
                      <Label htmlFor="include-trends" className="text-sm font-normal">
                        Trend Data
                      </Label>
                    </div>
                  </div>
                </div>

                <Separator />

                {/* Additional Fields */}
                <div className="space-y-3">
                  <Label>Additional Fields</Label>
                  <div className="grid grid-cols-2 gap-2">
                    {availableFields.map((field) => (
                      <div key={field} className="flex items-center space-x-2">
                        <Checkbox
                          id={`field-${field}`}
                          checked={exportOptions.customFields?.includes(field) || false}
                          onCheckedChange={(checked) => 
                            handleCustomFieldToggle(field, checked as boolean)
                          }
                        />
                        <Label htmlFor={`field-${field}`} className="text-xs font-normal capitalize">
                          {field.replace(/([A-Z])/g, ' $1').trim()}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Export Info */}
                <div className="bg-muted p-3 rounded-lg space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span>Format:</span>
                    <Badge variant="outline" className="flex items-center space-x-1">
                      {getFormatIcon(exportOptions.format)}
                      <span>{exportOptions.format.toUpperCase()}</span>
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span>Records:</span>
                    <span className="font-medium">{data.items.length}</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span>Est. Size:</span>
                    <span className="font-medium">{getEstimatedFileSize()}</span>
                  </div>
                </div>
              </div>
              
              <DialogFooter>
                <Button
                  onClick={handleExport}
                  disabled={isExporting}
                  className="flex items-center space-x-2"
                >
                  {isExporting ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white" />
                      <span>Exporting...</span>
                    </>
                  ) : exportSuccess ? (
                    <>
                      <CheckCircle className="h-4 w-4" />
                      <span>Exported!</span>
                    </>
                  ) : (
                    <>
                      <Download className="h-4 w-4" />
                      <span>Export Data</span>
                    </>
                  )}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>

        <Separator />

        {/* Export Statistics */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">
              {data.items.length}
            </div>
            <div className="text-sm text-muted-foreground">Total Records</div>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {data.items.filter(item => item.status === 'on-track').length}
            </div>
            <div className="text-sm text-muted-foreground">On Track</div>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-red-600">
              {data.items.filter(item => item.status === 'over').length}
            </div>
            <div className="text-sm text-muted-foreground">Over Budget</div>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-yellow-600">
              {data.items.filter(item => item.status === 'under').length}
            </div>
            <div className="text-sm text-muted-foreground">Under Budget</div>
          </div>
        </div>

        {/* Recent Exports */}
        <div className="space-y-2">
          <Label className="text-sm font-medium">Quick Actions</Label>
          <div className="flex flex-wrap gap-2">
            <Button
              variant="outline"
              size="sm"
              className="flex items-center space-x-2"
              onClick={() => {
                // In a real app, this would open email client with pre-filled data
                console.log('Email export functionality would be implemented here');
              }}
            >
              <Mail className="h-4 w-4" />
              <span>Email Report</span>
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              className="flex items-center space-x-2"
              onClick={() => {
                // In a real app, this would schedule automatic exports
                console.log('Schedule export functionality would be implemented here');
              }}
            >
              <Calendar className="h-4 w-4" />
              <span>Schedule Export</span>
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};