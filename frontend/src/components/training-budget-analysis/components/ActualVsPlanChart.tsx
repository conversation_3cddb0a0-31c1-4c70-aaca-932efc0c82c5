import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import { Badge } from '~/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '~/components/ui/tabs';
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  Pie<PERSON>hart,
  Pie,
  Cell,
  LineChart,
  Line
} from 'recharts';
import { 
  BarChart3, 
  Pie<PERSON>hart as PieChartIcon, 
  TrendingUp,
  Download,
  Maximize2
} from 'lucide-react';
import type { BudgetData } from '../types';

interface ActualVsPlanChartProps {
  data: BudgetData;
  showDetailed?: boolean;
  className?: string;
}

const COLORS = {
  planned: '#3b82f6',
  actual: '#10b981',
  variance: '#ef4444',
  over: '#ef4444',
  under: '#10b981',
  onTrack: '#f59e0b'
};

const PIE_COLORS = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#f97316'];

export const ActualVsPlanChart: React.FC<ActualVsPlanChartProps> = ({ 
  data, 
  showDetailed = false, 
  className 
}) => {
  const [chartType, setChartType] = useState<'bar' | 'pie' | 'line'>('bar');
  const [isFullscreen, setIsFullscreen] = useState(false);

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  const formatTooltip = (value: number, name: string) => {
    return [formatCurrency(value), name];
  };

  // Prepare data for bar chart
  const barChartData = data.items.map(item => ({
    category: item.category.length > 15 ? item.category.substring(0, 15) + '...' : item.category,
    fullCategory: item.category,
    planned: item.plannedAmount,
    actual: item.actualAmount,
    variance: item.variance,
    status: item.status
  }));

  // Prepare data for pie chart (by status)
  const statusData = [
    {
      name: 'Over Budget',
      value: data.items.filter(item => item.status === 'over').length,
      color: COLORS.over
    },
    {
      name: 'Under Budget',
      value: data.items.filter(item => item.status === 'under').length,
      color: COLORS.under
    },
    {
      name: 'On Track',
      value: data.items.filter(item => item.status === 'on-track').length,
      color: COLORS.onTrack
    }
  ].filter(item => item.value > 0);

  // Prepare data for spending distribution pie chart
  const spendingData = data.items.map((item, index) => ({
    name: item.category,
    value: item.actualAmount,
    color: PIE_COLORS[index % PIE_COLORS.length]
  }));

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-4 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-semibold text-gray-900">{payload[0]?.payload?.fullCategory || label}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} className="text-sm" style={{ color: entry.color }}>
              {entry.name}: {formatCurrency(entry.value)}
            </p>
          ))}
          {payload[0]?.payload?.status && (
            <Badge 
              variant={payload[0].payload.status === 'over' ? 'destructive' : 
                      payload[0].payload.status === 'on-track' ? 'default' : 'secondary'}
              className="mt-2"
            >
              {payload[0].payload.status === 'over' ? 'Over Budget' :
               payload[0].payload.status === 'on-track' ? 'On Track' : 'Under Budget'}
            </Badge>
          )}
        </div>
      );
    }
    return null;
  };

  const PieTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-semibold text-gray-900">{payload[0].name}</p>
          <p className="text-sm" style={{ color: payload[0].color }}>
            {typeof payload[0].value === 'number' && payload[0].value < 100 
              ? `${payload[0].value} categories`
              : formatCurrency(payload[0].value)
            }
          </p>
        </div>
      );
    }
    return null;
  };

  const renderBarChart = () => (
    <ResponsiveContainer width="100%" height={showDetailed ? 500 : 400}>
      <BarChart data={barChartData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
        <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
        <XAxis 
          dataKey="category" 
          tick={{ fontSize: 12 }}
          angle={-45}
          textAnchor="end"
          height={80}
        />
        <YAxis 
          tick={{ fontSize: 12 }}
          tickFormatter={formatCurrency}
        />
        <Tooltip content={<CustomTooltip />} />
        <Legend />
        <Bar 
          dataKey="planned" 
          name="Planned" 
          fill={COLORS.planned}
          radius={[2, 2, 0, 0]}
        />
        <Bar 
          dataKey="actual" 
          name="Actual" 
          fill={COLORS.actual}
          radius={[2, 2, 0, 0]}
        />
      </BarChart>
    </ResponsiveContainer>
  );

  const renderPieChart = () => (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <div>
        <h4 className="text-sm font-medium mb-4 text-center">Budget Status Distribution</h4>
        <ResponsiveContainer width="100%" height={300}>
          <PieChart>
            <Pie
              data={statusData}
              cx="50%"
              cy="50%"
              outerRadius={100}
              dataKey="value"
              label={({ name, value }) => `${name}: ${value}`}
            >
              {statusData.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={entry.color} />
              ))}
            </Pie>
            <Tooltip content={<PieTooltip />} />
          </PieChart>
        </ResponsiveContainer>
      </div>
      
      <div>
        <h4 className="text-sm font-medium mb-4 text-center">Spending Distribution</h4>
        <ResponsiveContainer width="100%" height={300}>
          <PieChart>
            <Pie
              data={spendingData}
              cx="50%"
              cy="50%"
              outerRadius={100}
              dataKey="value"
              label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
            >
              {spendingData.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={entry.color} />
              ))}
            </Pie>
            <Tooltip content={<PieTooltip />} />
          </PieChart>
        </ResponsiveContainer>
      </div>
    </div>
  );

  const renderLineChart = () => (
    <ResponsiveContainer width="100%" height={showDetailed ? 500 : 400}>
      <LineChart data={barChartData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
        <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
        <XAxis 
          dataKey="category" 
          tick={{ fontSize: 12 }}
          angle={-45}
          textAnchor="end"
          height={80}
        />
        <YAxis 
          tick={{ fontSize: 12 }}
          tickFormatter={formatCurrency}
        />
        <Tooltip content={<CustomTooltip />} />
        <Legend />
        <Line 
          type="monotone" 
          dataKey="planned" 
          name="Planned" 
          stroke={COLORS.planned}
          strokeWidth={3}
          dot={{ r: 6 }}
        />
        <Line 
          type="monotone" 
          dataKey="actual" 
          name="Actual" 
          stroke={COLORS.actual}
          strokeWidth={3}
          dot={{ r: 6 }}
        />
      </LineChart>
    </ResponsiveContainer>
  );

  return (
    <Card className={`${className} ${isFullscreen ? 'fixed inset-4 z-50 overflow-auto' : ''}`}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <BarChart3 className="h-6 w-6 text-primary" />
            <span>Actual vs Planned Budget</span>
          </CardTitle>
          
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsFullscreen(!isFullscreen)}
            >
              <Maximize2 className="h-4 w-4" />
            </Button>
            
            <Button variant="outline" size="sm">
              <Download className="h-4 w-4" />
            </Button>
          </div>
        </div>
        
        {showDetailed && (
          <Tabs value={chartType} onChange={(value: string | null) => setChartType(value as 'line' | 'bar' | 'pie')}>
            <TabsList>
              <TabsTrigger value="bar" className="flex items-center space-x-1">
                <BarChart3 className="h-4 w-4" />
                <span>Bar Chart</span>
              </TabsTrigger>
              <TabsTrigger value="pie" className="flex items-center space-x-1">
                <PieChartIcon className="h-4 w-4" />
                <span>Pie Charts</span>
              </TabsTrigger>
              <TabsTrigger value="line" className="flex items-center space-x-1">
                <TrendingUp className="h-4 w-4" />
                <span>Line Chart</span>
              </TabsTrigger>
            </TabsList>
          </Tabs>
        )}
      </CardHeader>
      
      <CardContent>
        {showDetailed ? (
          <Tabs value={chartType}>
            <TabsContent value="bar">{renderBarChart()}</TabsContent>
            <TabsContent value="pie">{renderPieChart()}</TabsContent>
            <TabsContent value="line">{renderLineChart()}</TabsContent>
          </Tabs>
        ) : (
          renderBarChart()
        )}
        
        {/* Summary Stats */}
        <div className="mt-6 grid grid-cols-2 lg:grid-cols-4 gap-4 pt-4 border-t">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">
              {formatCurrency(data.totalPlanned)}
            </div>
            <div className="text-sm text-muted-foreground">Total Planned</div>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {formatCurrency(data.totalActual)}
            </div>
            <div className="text-sm text-muted-foreground">Total Actual</div>
          </div>
          
          <div className="text-center">
            <div className={`text-2xl font-bold ${
              data.totalVariance > 0 ? 'text-red-600' : 'text-green-600'
            }`}>
              {data.totalVariance > 0 ? '+' : ''}{formatCurrency(data.totalVariance)}
            </div>
            <div className="text-sm text-muted-foreground">Variance</div>
          </div>
          
          <div className="text-center">
            <div className={`text-2xl font-bold ${
              data.totalVariancePercentage > 0 ? 'text-red-600' : 'text-green-600'
            }`}>
              {data.totalVariancePercentage > 0 ? '+' : ''}{data.totalVariancePercentage.toFixed(1)}%
            </div>
            <div className="text-sm text-muted-foreground">% Variance</div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};