import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import { Input } from '~/components/ui/input';
import { Label } from '~/components/ui/label';
import { Badge } from '~/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select';
import { Checkbox } from '~/components/ui/checkbox';
import { Separator } from '~/components/ui/separator';
import { 
  Search, 
  Filter, 
  X, 
  Calendar,
  DollarSign,
  Building,
  Tag,
  RotateCcw
} from 'lucide-react';
import type { FilterOptions, BudgetData } from '../types';

interface BudgetFiltersProps {
  filters: FilterOptions;
  onFiltersChange: (filters: FilterOptions) => void;
  data: BudgetData;
  className?: string;
}

export const BudgetFilters: React.FC<BudgetFiltersProps> = ({ 
  filters, 
  onFiltersChange, 
  data, 
  className 
}) => {
  const handleFilterChange = (key: keyof FilterOptions, value: any) => {
    onFiltersChange({
      ...filters,
      [key]: value
    });
  };

  const handleArrayFilterChange = (key: keyof FilterOptions, value: string, checked: boolean) => {
    const currentArray = (filters[key] as string[]) || [];
    const newArray = checked 
      ? [...currentArray, value]
      : currentArray.filter(item => item !== value);
    
    handleFilterChange(key, newArray);
  };

  const clearFilters = () => {
    onFiltersChange({
      searchTerm: '',
      departments: [],
      categories: [],
      status: [],
      statuses: [],
      dateRange: { start: new Date(), end: new Date() },
      amountRange: { min: 0, max: 0 },
      sortBy: 'category',
      sortOrder: 'asc'
    });
  };

  const getUniqueValues = (key: 'department' | 'category') => {
    return [...new Set(data.items.map(item => item[key]).filter(Boolean))];
  };

  const departments = getUniqueValues('department');
  const categories = getUniqueValues('category');
  const statuses = ['on-track', 'over', 'under', 'at-risk'] as const;

  const getActiveFiltersCount = () => {
    let count = 0;
    if (filters.searchTerm) count++;
    if (filters.departments?.length) count++;
    if (filters.categories?.length) count++;
    if (filters.statuses?.length) count++;
    if (filters.dateRange?.start || filters.dateRange?.end) count++;
    if (filters.amountRange?.min || filters.amountRange?.max) count++;
    return count;
  };

  const activeFiltersCount = getActiveFiltersCount();

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'on-track': return 'bg-green-100 text-green-800';
      case 'over': return 'bg-red-100 text-red-800';
      case 'under': return 'bg-blue-100 text-blue-800';
      case 'at-risk': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <Filter className="h-5 w-5" />
            <span>Filters</span>
            {activeFiltersCount > 0 && (
              <Badge variant="secondary" className="ml-2">
                {activeFiltersCount} active
              </Badge>
            )}
          </CardTitle>
          {activeFiltersCount > 0 && (
            <Button
              variant="outline"
              size="sm"
              onClick={clearFilters}
              className="flex items-center space-x-1"
            >
              <RotateCcw className="h-4 w-4" />
              <span>Clear All</span>
            </Button>
          )}
        </div>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Search */}
        <div className="space-y-2">
          <Label className="flex items-center space-x-2">
            <Search className="h-4 w-4" />
            <span>Search</span>
          </Label>
          <div className="relative">
            <Input
              placeholder="Search by category, department, or description..."
              value={filters.searchTerm || ''}
              onChange={(e) => handleFilterChange('searchTerm', e.target.value)}
              className="pl-10"
            />
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            {filters.searchTerm && (
              <Button
                variant="ghost"
                size="sm"
                className="absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
                onClick={() => handleFilterChange('searchTerm', '')}
              >
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>

        <Separator />

        {/* Departments */}
        <div className="space-y-3">
          <Label className="flex items-center space-x-2">
            <Building className="h-4 w-4" />
            <span>Departments</span>
            {filters.departments?.length > 0 && (
              <Badge variant="outline" className="ml-2">
                {filters.departments.length}
              </Badge>
            )}
          </Label>
          <div className="grid grid-cols-1 gap-2 max-h-32 overflow-y-auto">
            {departments.map((department) => (
              <div key={department} className="flex items-center space-x-2">
                <Checkbox
                  id={`dept-${department}`}
                  checked={filters.departments?.includes(department) || false}
                  onChange={(checked: boolean) => 
                    handleArrayFilterChange('departments', department as string, checked)
                  }
                />
                <Label 
                  htmlFor={`dept-${department}`} 
                  className="text-sm font-normal cursor-pointer flex-1"
                >
                  {department}
                </Label>
              </div>
            ))}
          </div>
        </div>

        <Separator />

        {/* Categories */}
        <div className="space-y-3">
          <Label className="flex items-center space-x-2">
            <Tag className="h-4 w-4" />
            <span>Categories</span>
            {filters.categories?.length > 0 && (
              <Badge variant="outline" className="ml-2">
                {filters.categories.length}
              </Badge>
            )}
          </Label>
          <div className="grid grid-cols-1 gap-2 max-h-32 overflow-y-auto">
            {categories.map((category) => (
              <div key={category} className="flex items-center space-x-2">
                <Checkbox
                  id={`cat-${category}`}
                  checked={filters.categories?.includes(category) || false}
                  onChange={(checked: boolean) => 
                    handleArrayFilterChange('categories', category as string, checked)
                  }
                />
                <Label 
                  htmlFor={`cat-${category}`} 
                  className="text-sm font-normal cursor-pointer flex-1"
                >
                  {category}
                </Label>
              </div>
            ))}
          </div>
        </div>

        <Separator />

        {/* Status */}
        <div className="space-y-3">
          <Label>Budget Status</Label>
          <div className="grid grid-cols-1 gap-2">
            {statuses.map((status) => (
              <div key={status} className="flex items-center space-x-2">
                <Checkbox
                  id={`status-${status}`}
                  checked={filters.statuses?.includes(status) || false}
                  onChange={(checked: boolean) => 
                    handleArrayFilterChange('statuses', status, checked)
                  }
                />
                <Label 
                  htmlFor={`status-${status}`} 
                  className="text-sm font-normal cursor-pointer flex-1 flex items-center space-x-2"
                >
                  <Badge className={getStatusColor(status)}>
                    {status.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                  </Badge>
                </Label>
              </div>
            ))}
          </div>
        </div>

        <Separator />

        {/* Date Range */}
        <div className="space-y-3">
          <Label className="flex items-center space-x-2">
            <Calendar className="h-4 w-4" />
            <span>Date Range</span>
          </Label>
          <div className="grid grid-cols-2 gap-2">
            <div>
              <Label htmlFor="start-date" className="text-xs text-muted-foreground">From</Label>
              <Input
                id="start-date"
                type="date"
                value={filters.dateRange?.start ? filters.dateRange.start.toISOString().split('T')[0] : ''}
                onChange={(e) => handleFilterChange('dateRange', {
                  ...filters.dateRange,
                  start: e.target.value ? new Date(e.target.value) : new Date()
                })}
              />
            </div>
            <div>
              <Label htmlFor="end-date" className="text-xs text-muted-foreground">To</Label>
              <Input
                id="end-date"
                type="date"
                value={filters.dateRange?.end ? filters.dateRange.end.toISOString().split('T')[0] : ''}
                onChange={(e) => handleFilterChange('dateRange', {
                  ...filters.dateRange,
                  end: e.target.value ? new Date(e.target.value) : new Date()
                })}
              />
            </div>
          </div>
        </div>

        <Separator />

        {/* Amount Range */}
        <div className="space-y-3">
          <Label className="flex items-center space-x-2">
            <DollarSign className="h-4 w-4" />
            <span>Amount Range</span>
          </Label>
          <div className="grid grid-cols-2 gap-2">
            <div>
              <Label htmlFor="min-amount" className="text-xs text-muted-foreground">Min Amount</Label>
              <Input
                id="min-amount"
                type="number"
                placeholder="0"
                value={filters.amountRange?.min || ''}
                onChange={(e) => handleFilterChange('amountRange', {
                  ...filters.amountRange,
                  min: Number(e.target.value) || 0
                })}
              />
            </div>
            <div>
              <Label htmlFor="max-amount" className="text-xs text-muted-foreground">Max Amount</Label>
              <Input
                id="max-amount"
                type="number"
                placeholder="No limit"
                value={filters.amountRange?.max || ''}
                onChange={(e) => handleFilterChange('amountRange', {
                  ...filters.amountRange,
                  max: Number(e.target.value) || 0
                })}
              />
            </div>
          </div>
          {(filters.amountRange?.min || filters.amountRange?.max) && (
            <div className="text-xs text-muted-foreground">
              Range: {formatCurrency(filters.amountRange?.min || 0)} - {filters.amountRange?.max ? formatCurrency(filters.amountRange.max) : 'No limit'}
            </div>
          )}
        </div>

        <Separator />

        {/* Sort Options */}
        <div className="space-y-3">
          <Label>Sort Options</Label>
          <div className="grid grid-cols-2 gap-2">
            <div>
              <Label htmlFor="sort-by" className="text-xs text-muted-foreground">Sort By</Label>
              <Select 
                value={filters.sortBy || 'category'} 
                onValueChange={(value) => handleFilterChange('sortBy', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="category">Category</SelectItem>
                  <SelectItem value="department">Department</SelectItem>
                  <SelectItem value="plannedAmount">Planned Amount</SelectItem>
                  <SelectItem value="actualAmount">Actual Amount</SelectItem>
                  <SelectItem value="variance">Variance</SelectItem>
                  <SelectItem value="variancePercentage">Variance %</SelectItem>
                  <SelectItem value="status">Status</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="sort-order" className="text-xs text-muted-foreground">Order</Label>
              <Select 
                value={filters.sortOrder || 'asc'} 
                onValueChange={(value) => handleFilterChange('sortOrder', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="asc">Ascending</SelectItem>
                  <SelectItem value="desc">Descending</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        {/* Active Filters Summary */}
        {activeFiltersCount > 0 && (
          <>
            <Separator />
            <div className="space-y-2">
              <Label className="text-sm font-medium">Active Filters</Label>
              <div className="flex flex-wrap gap-2">
                {filters.searchTerm && (
                  <Badge variant="secondary" className="flex items-center space-x-1">
                    <span>Search: {filters.searchTerm}</span>
                    <X 
                      className="h-3 w-3 cursor-pointer" 
                      onClick={() => handleFilterChange('searchTerm', '')}
                    />
                  </Badge>
                )}
                {filters.departments?.map((dept) => (
                  <Badge key={dept} variant="secondary" className="flex items-center space-x-1">
                    <span>{dept}</span>
                    <X 
                      className="h-3 w-3 cursor-pointer" 
                      onClick={() => handleArrayFilterChange('departments', dept, false)}
                    />
                  </Badge>
                ))}
                {filters.categories?.map((cat) => (
                  <Badge key={cat} variant="secondary" className="flex items-center space-x-1">
                    <span>{cat}</span>
                    <X 
                      className="h-3 w-3 cursor-pointer" 
                      onClick={() => handleArrayFilterChange('categories', cat, false)}
                    />
                  </Badge>
                ))}
                {filters.statuses?.map((status) => (
                  <Badge key={status} variant="secondary" className="flex items-center space-x-1">
                    <span>{status}</span>
                    <X 
                      className="h-3 w-3 cursor-pointer" 
                      onClick={() => handleArrayFilterChange('statuses', status, false)}
                    />
                  </Badge>
                ))}
              </div>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
};