import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON><PERSON>, CardTitle } from '~/components/ui/card';
import { Progress } from '~/components/ui/progress';
import { Badge } from '~/components/ui/badge';
import { Separator } from '~/components/ui/separator';
import { 
  DollarSign, 
  TrendingUp, 
  TrendingDown, 
  Target,
  AlertCircle,
  CheckCircle
} from 'lucide-react';
import type { BudgetData } from '../types';

interface BudgetOverviewCardProps {
  data: BudgetData;
  className?: string;
}

export const BudgetOverviewCard: React.FC<BudgetOverviewCardProps> = ({ data, className }) => {
  const utilizationPercentage = (data.totalActual / data.totalPlanned) * 100;
  const isOverBudget = data.totalActual > data.totalPlanned;
  const isOnTrack = Math.abs(data.totalVariancePercentage) <= 5;

  const getStatusIcon = () => {
    if (isOverBudget) return <AlertCircle className="h-5 w-5 text-red-500" />;
    if (isOnTrack) return <CheckCircle className="h-5 w-5 text-green-500" />;
    return <Target className="h-5 w-5 text-yellow-500" />;
  };

  const getStatusBadge = () => {
    if (isOverBudget) return <Badge variant="destructive">Over Budget</Badge>;
    if (isOnTrack) return <Badge variant="default" className="bg-green-500">On Track</Badge>;
    return <Badge variant="secondary">Under Budget</Badge>;
  };

  const getVarianceIcon = () => {
    if (data.totalVariance > 0) return <TrendingUp className="h-4 w-4 text-red-500" />;
    if (data.totalVariance < 0) return <TrendingDown className="h-4 w-4 text-green-500" />;
    return <Target className="h-4 w-4 text-blue-500" />;
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  return (
    <Card className={`${className} border-l-4 ${isOverBudget ? 'border-l-red-500' : isOnTrack ? 'border-l-green-500' : 'border-l-yellow-500'}`}>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <DollarSign className="h-6 w-6 text-primary" />
            <span>Budget Overview</span>
          </div>
          {getStatusIcon()}
        </CardTitle>
        <div className="flex items-center justify-between">
          {getStatusBadge()}
          <span className="text-sm text-muted-foreground">
            Last updated: {data.lastUpdated.toLocaleDateString()}
          </span>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Budget Utilization */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Budget Utilization</span>
            <span className="text-sm font-bold">{utilizationPercentage.toFixed(1)}%</span>
          </div>
          <Progress 
            value={Math.min(utilizationPercentage, 100)} 
            className="h-3"
          />
          {utilizationPercentage > 100 && (
            <div className="text-xs text-red-600 font-medium">
              Exceeded by {(utilizationPercentage - 100).toFixed(1)}%
            </div>
          )}
        </div>

        <Separator />

        {/* Financial Summary */}
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <div className="text-sm text-muted-foreground">Planned Budget</div>
            <div className="text-2xl font-bold text-blue-600">
              {formatCurrency(data.totalPlanned)}
            </div>
          </div>
          
          <div className="space-y-2">
            <div className="text-sm text-muted-foreground">Actual Spend</div>
            <div className={`text-2xl font-bold ${
              isOverBudget ? 'text-red-600' : 'text-green-600'
            }`}>
              {formatCurrency(data.totalActual)}
            </div>
          </div>
        </div>

        <Separator />

        {/* Variance Analysis */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Variance</span>
            <div className="flex items-center space-x-1">
              {getVarianceIcon()}
              <span className={`text-sm font-bold ${
                data.totalVariance > 0 ? 'text-red-600' : data.totalVariance < 0 ? 'text-green-600' : 'text-blue-600'
              }`}>
                {data.totalVariance > 0 ? '+' : ''}{formatCurrency(data.totalVariance)}
              </span>
            </div>
          </div>
          
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Percentage Variance</span>
            <span className={`text-sm font-bold ${
              data.totalVariancePercentage > 0 ? 'text-red-600' : data.totalVariancePercentage < 0 ? 'text-green-600' : 'text-blue-600'
            }`}>
              {data.totalVariancePercentage > 0 ? '+' : ''}{data.totalVariancePercentage.toFixed(1)}%
            </span>
          </div>
        </div>

        <Separator />

        {/* Quick Stats */}
        <div className="grid grid-cols-3 gap-3 text-center">
          <div className="space-y-1">
            <div className="text-xs text-muted-foreground">Categories</div>
            <div className="text-lg font-bold">
              {new Set(data.items.map(item => item.category)).size}
            </div>
          </div>
          
          <div className="space-y-1">
            <div className="text-xs text-muted-foreground">Over Budget</div>
            <div className="text-lg font-bold text-red-600">
              {data.items.filter(item => item.status === 'over').length}
            </div>
          </div>
          
          <div className="space-y-1">
            <div className="text-xs text-muted-foreground">On Track</div>
            <div className="text-lg font-bold text-green-600">
              {data.items.filter(item => item.status === 'on-track').length}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};