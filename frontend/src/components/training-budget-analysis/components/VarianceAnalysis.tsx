import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '~/components/ui/card';
import { Badge } from '~/components/ui/badge';
import { Progress } from '~/components/ui/progress';
import { Alert, AlertDescription } from '~/components/ui/alert';
import { 
  TrendingUp, 
  TrendingDown, 
  AlertTriangle, 
  Target,
  Activity
} from 'lucide-react';
import type { BudgetData } from '../types';

interface VarianceAnalysisProps {
  data: BudgetData;
  className?: string;
}

export const VarianceAnalysis: React.FC<VarianceAnalysisProps> = ({ data, className }) => {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const getVarianceAnalysis = () => {
    const overBudgetItems = data.items.filter(item => item.status === 'over');
    const underBudgetItems = data.items.filter(item => item.status === 'under');
    const onTrackItems = data.items.filter(item => item.status === 'on-track');
    
    const totalOverBudget = overBudgetItems.reduce((sum, item) => sum + Math.abs(item.variance), 0);
    const totalUnderBudget = underBudgetItems.reduce((sum, item) => sum + Math.abs(item.variance), 0);
    
    return {
      overBudgetItems,
      underBudgetItems,
      onTrackItems,
      totalOverBudget,
      totalUnderBudget,
      netVariance: data.totalVariance
    };
  };

  const analysis = getVarianceAnalysis();
  
  const getTopVariances = (type: 'positive' | 'negative') => {
    return data.items
      .filter(item => type === 'positive' ? item.variance > 0 : item.variance < 0)
      .sort((a, b) => type === 'positive' ? b.variance - a.variance : a.variance - b.variance)
      .slice(0, 5);
  };

  const positiveVariances = getTopVariances('positive');
  const negativeVariances = getTopVariances('negative');

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="border-l-4 border-l-red-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Over Budget</p>
                <p className="text-2xl font-bold text-red-600">
                  {formatCurrency(analysis.totalOverBudget)}
                </p>
                <p className="text-sm text-muted-foreground mt-1">
                  {analysis.overBudgetItems.length} categories
                </p>
              </div>
              <div className="p-3 rounded-full bg-red-100">
                <TrendingUp className="h-6 w-6 text-red-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-green-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Under Budget</p>
                <p className="text-2xl font-bold text-green-600">
                  {formatCurrency(analysis.totalUnderBudget)}
                </p>
                <p className="text-sm text-muted-foreground mt-1">
                  {analysis.underBudgetItems.length} categories
                </p>
              </div>
              <div className="p-3 rounded-full bg-green-100">
                <TrendingDown className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-blue-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Net Variance</p>
                <p className={`text-2xl font-bold ${
                  analysis.netVariance > 0 ? 'text-red-600' : analysis.netVariance < 0 ? 'text-green-600' : 'text-blue-600'
                }`}>
                  {analysis.netVariance > 0 ? '+' : ''}{formatCurrency(analysis.netVariance)}
                </p>
                <p className="text-sm text-muted-foreground mt-1">
                  {data.totalVariancePercentage > 0 ? '+' : ''}{data.totalVariancePercentage.toFixed(1)}%
                </p>
              </div>
              <div className="p-3 rounded-full bg-blue-100">
                <Activity className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Variance Alerts */}
      {analysis.netVariance > 0 && (
        <Alert className="border-red-200 bg-red-50">
          <AlertTriangle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-800">
            <strong>Budget Overrun Alert:</strong> Total spending exceeds planned budget by {formatCurrency(analysis.netVariance)} ({data.totalVariancePercentage.toFixed(1)}%). 
            Immediate attention required for {analysis.overBudgetItems.length} categories.
          </AlertDescription>
        </Alert>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Top Positive Variances */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2 text-red-600">
              <TrendingUp className="h-5 w-5" />
              <span>Highest Over-Budget Categories</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {positiveVariances.length > 0 ? (
              positiveVariances.map((item, index) => (
                <div key={item.id} className="flex items-center justify-between p-3 bg-red-50 rounded-lg">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <Badge variant="outline" className="text-xs">
                        #{index + 1}
                      </Badge>
                      <span className="font-medium">{item.category}</span>
                    </div>
                    <div className="text-sm text-muted-foreground mt-1">
                      {item.department && `${item.department} • `}
                      {item.variancePercentage.toFixed(1)}% over budget
                    </div>
                    <Progress 
                      value={Math.min((item.variance / item.plannedAmount) * 100, 100)} 
                      className="mt-2 h-2"
                    />
                  </div>
                  <div className="text-right ml-4">
                    <div className="font-bold text-red-600">
                      +{formatCurrency(item.variance)}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {formatCurrency(item.actualAmount)} / {formatCurrency(item.plannedAmount)}
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                <Target className="h-12 w-12 mx-auto mb-2 text-green-500" />
                <p>No categories are over budget!</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Top Negative Variances */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2 text-green-600">
              <TrendingDown className="h-5 w-5" />
              <span>Highest Under-Budget Categories</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {negativeVariances.length > 0 ? (
              negativeVariances.map((item, index) => (
                <div key={item.id} className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <Badge variant="outline" className="text-xs">
                        #{index + 1}
                      </Badge>
                      <span className="font-medium">{item.category}</span>
                    </div>
                    <div className="text-sm text-muted-foreground mt-1">
                      {item.department && `${item.department} • `}
                      {Math.abs(item.variancePercentage).toFixed(1)}% under budget
                    </div>
                    <Progress 
                      value={(item.actualAmount / item.plannedAmount) * 100} 
                      className="mt-2 h-2"
                    />
                  </div>
                  <div className="text-right ml-4">
                    <div className="font-bold text-green-600">
                      {formatCurrency(item.variance)}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {formatCurrency(item.actualAmount)} / {formatCurrency(item.plannedAmount)}
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                <Target className="h-12 w-12 mx-auto mb-2 text-blue-500" />
                <p>No significant under-budget categories found.</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Variance Distribution */}
      <Card>
        <CardHeader>
          <CardTitle>Variance Distribution Analysis</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="text-3xl font-bold text-red-600 mb-2">
                {analysis.overBudgetItems.length}
              </div>
              <div className="text-sm text-muted-foreground mb-3">Categories Over Budget</div>
              <Progress 
                value={(analysis.overBudgetItems.length / data.items.length) * 100} 
                className="h-2"
              />
              <div className="text-xs text-muted-foreground mt-1">
                {((analysis.overBudgetItems.length / data.items.length) * 100).toFixed(1)}% of total
              </div>
            </div>
            
            <div className="text-center">
              <div className="text-3xl font-bold text-green-600 mb-2">
                {analysis.underBudgetItems.length}
              </div>
              <div className="text-sm text-muted-foreground mb-3">Categories Under Budget</div>
              <Progress 
                value={(analysis.underBudgetItems.length / data.items.length) * 100} 
                className="h-2"
              />
              <div className="text-xs text-muted-foreground mt-1">
                {((analysis.underBudgetItems.length / data.items.length) * 100).toFixed(1)}% of total
              </div>
            </div>
            
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-600 mb-2">
                {analysis.onTrackItems.length}
              </div>
              <div className="text-sm text-muted-foreground mb-3">Categories On Track</div>
              <Progress 
                value={(analysis.onTrackItems.length / data.items.length) * 100} 
                className="h-2"
              />
              <div className="text-xs text-muted-foreground mt-1">
                {((analysis.onTrackItems.length / data.items.length) * 100).toFixed(1)}% of total
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};