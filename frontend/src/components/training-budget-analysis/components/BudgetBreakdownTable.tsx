import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { But<PERSON> } from '~/components/ui/button';
import { Input } from '~/components/ui/input';
import { Badge } from '~/components/ui/badge';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '~/components/ui/table';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '~/components/ui/dropdown-menu';
import { 
  Search, 
  Filter, 
  Download, 
  ArrowUpDown, 
  ArrowUp, 
  ArrowDown,
  MoreHorizontal,
  TrendingUp,
  TrendingDown,
  Target
} from 'lucide-react';
import type { BudgetData, BudgetItem } from '../types';

interface BudgetBreakdownTableProps {
  data: BudgetData;
  className?: string;
}

type SortField = 'category' | 'plannedAmount' | 'actualAmount' | 'variance' | 'variancePercentage';
type SortDirection = 'asc' | 'desc';

export const BudgetBreakdownTable: React.FC<BudgetBreakdownTableProps> = ({ data, className }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [sortField, setSortField] = useState<SortField>('variance');
  const [sortDirection, setSortDirection] = useState<SortDirection>('desc');
  const [statusFilter, setStatusFilter] = useState<string>('all');

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const getStatusBadge = (status: BudgetItem['status']) => {
    switch (status) {
      case 'over':
        return <Badge variant="destructive">Over Budget</Badge>;
      case 'under':
        return <Badge variant="secondary">Under Budget</Badge>;
      case 'on-track':
        return <Badge variant="default" className="bg-green-500">On Track</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  const getVarianceIcon = (variance: number) => {
    if (variance > 0) return <TrendingUp className="h-4 w-4 text-red-500" />;
    if (variance < 0) return <TrendingDown className="h-4 w-4 text-green-500" />;
    return <Target className="h-4 w-4 text-blue-500" />;
  };

  const filteredAndSortedData = useMemo(() => {
    let filtered = data.items.filter(item => {
      const matchesSearch = item.category.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           (item.subcategory?.toLowerCase().includes(searchTerm.toLowerCase())) ||
                           (item.department?.toLowerCase().includes(searchTerm.toLowerCase()));
      
      const matchesStatus = statusFilter === 'all' || item.status === statusFilter;
      
      return matchesSearch && matchesStatus;
    });

    return filtered.sort((a, b) => {
      let aValue: any = a[sortField];
      let bValue: any = b[sortField];
      
      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }
      
      if (sortDirection === 'asc') {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      }
    });
  }, [data.items, searchTerm, sortField, sortDirection, statusFilter]);

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('desc');
    }
  };

  const getSortIcon = (field: SortField) => {
    if (sortField !== field) {
      return <ArrowUpDown className="h-4 w-4 text-gray-400" />;
    }
    return sortDirection === 'asc' ? 
      <ArrowUp className="h-4 w-4 text-primary" /> : 
      <ArrowDown className="h-4 w-4 text-primary" />;
  };

  const exportToCSV = () => {
    const headers = ['Category', 'Subcategory', 'Department', 'Planned Amount', 'Actual Amount', 'Variance', 'Variance %', 'Status'];
    const csvData = filteredAndSortedData.map(item => [
      item.category,
      item.subcategory || '',
      item.department || '',
      item.plannedAmount,
      item.actualAmount,
      item.variance,
      item.variancePercentage,
      item.status
    ]);
    
    const csvContent = [headers, ...csvData]
      .map(row => row.map(cell => `"${cell}"`).join(','))
      .join('\n');
    
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'budget-breakdown.csv';
    a.click();
    window.URL.revokeObjectURL(url);
  };

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
          <CardTitle className="flex items-center space-x-2">
            <Filter className="h-6 w-6 text-primary" />
            <span>Budget Breakdown</span>
          </CardTitle>
          
          <div className="flex items-center space-x-3">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search categories, departments..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 w-64"
              />
            </div>
            
            <DropdownMenu>
              <DropdownMenuTrigger>
                <Button variant="outline" size="sm">
                  <Filter className="h-4 w-4 mr-2" />
                  Status: {statusFilter === 'all' ? 'All' : statusFilter}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem onClick={() => setStatusFilter('all')}>
                  All Statuses
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setStatusFilter('over')}>
                  Over Budget
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setStatusFilter('under')}>
                  Under Budget
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setStatusFilter('on-track')}>
                  On Track
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
            
            <Button variant="outline" size="sm" onClick={exportToCSV}>
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
          </div>
        </div>
        
        <div className="flex items-center justify-between text-sm text-muted-foreground">
          <span>Showing {filteredAndSortedData.length} of {data.items.length} items</span>
          <span>Last updated: {data.lastUpdated.toLocaleDateString()}</span>
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="rounded-md border overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead 
                  className="cursor-pointer hover:bg-gray-50 select-none"
                  onClick={() => handleSort('category')}
                >
                  <div className="flex items-center space-x-1">
                    <span>Category</span>
                    {getSortIcon('category')}
                  </div>
                </TableHead>
                <TableHead>Department</TableHead>
                <TableHead 
                  className="cursor-pointer hover:bg-gray-50 select-none text-right"
                  onClick={() => handleSort('plannedAmount')}
                >
                  <div className="flex items-center justify-end space-x-1">
                    <span>Planned</span>
                    {getSortIcon('plannedAmount')}
                  </div>
                </TableHead>
                <TableHead 
                  className="cursor-pointer hover:bg-gray-50 select-none text-right"
                  onClick={() => handleSort('actualAmount')}
                >
                  <div className="flex items-center justify-end space-x-1">
                    <span>Actual</span>
                    {getSortIcon('actualAmount')}
                  </div>
                </TableHead>
                <TableHead 
                  className="cursor-pointer hover:bg-gray-50 select-none text-right"
                  onClick={() => handleSort('variance')}
                >
                  <div className="flex items-center justify-end space-x-1">
                    <span>Variance</span>
                    {getSortIcon('variance')}
                  </div>
                </TableHead>
                <TableHead 
                  className="cursor-pointer hover:bg-gray-50 select-none text-right"
                  onClick={() => handleSort('variancePercentage')}
                >
                  <div className="flex items-center justify-end space-x-1">
                    <span>Variance %</span>
                    {getSortIcon('variancePercentage')}
                  </div>
                </TableHead>
                <TableHead>Status</TableHead>
                <TableHead className="w-12"></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredAndSortedData.map((item) => (
                <TableRow key={item.id} className="hover:bg-gray-50">
                  <TableCell>
                    <div>
                      <div className="font-medium">{item.category}</div>
                      {item.subcategory && (
                        <div className="text-sm text-muted-foreground">{item.subcategory}</div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <span className="text-sm">{item.department || '-'}</span>
                  </TableCell>
                  <TableCell className="text-right font-mono">
                    {formatCurrency(item.plannedAmount)}
                  </TableCell>
                  <TableCell className="text-right font-mono">
                    <span className={item.actualAmount > item.plannedAmount ? 'text-red-600' : 'text-green-600'}>
                      {formatCurrency(item.actualAmount)}
                    </span>
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex items-center justify-end space-x-1">
                      {getVarianceIcon(item.variance)}
                      <span className={`font-mono ${
                        item.variance > 0 ? 'text-red-600' : item.variance < 0 ? 'text-green-600' : 'text-blue-600'
                      }`}>
                        {item.variance > 0 ? '+' : ''}{formatCurrency(item.variance)}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell className="text-right">
                    <span className={`font-mono ${
                      item.variancePercentage > 0 ? 'text-red-600' : item.variancePercentage < 0 ? 'text-green-600' : 'text-blue-600'
                    }`}>
                      {item.variancePercentage > 0 ? '+' : ''}{item.variancePercentage.toFixed(1)}%
                    </span>
                  </TableCell>
                  <TableCell>
                    {getStatusBadge(item.status)}
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent>
                        <DropdownMenuItem>
                          View Details
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          Edit Budget
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          Add Note
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
        
        {filteredAndSortedData.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            No budget items found matching your criteria.
          </div>
        )}
      </CardContent>
    </Card>
  );
};