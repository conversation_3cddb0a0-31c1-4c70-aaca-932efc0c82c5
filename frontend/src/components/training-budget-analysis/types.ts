/**
 * Training Budget Analysis Types
 */

export interface BudgetItem {
  id: string;
  category: string;
  subcategory?: string;
  plannedAmount: number;
  actualAmount: number;
  variance: number;
  variancePercentage: number;
  status: 'under' | 'over' | 'on-track';
  period: string;
  department?: string;
  description?: string;
  lastUpdated: Date;
}

export interface BudgetData {
  totalPlanned: number;
  totalActual: number;
  totalVariance: number;
  totalVariancePercentage: number;
  items: BudgetItem[];
  lastUpdated: Date;
  utilizationRate: number;
}

export interface VarianceData {
  category: string;
  variance: number;
  variancePercentage: number;
  impact: 'high' | 'medium' | 'low';
  trend: 'improving' | 'worsening' | 'stable';
}

export interface ChartDataPoint {
  period: string;
  planned: number;
  actual: number;
  variance: number;
}

export interface FilterOptions {
  dateRange: {
    start: Date;
    end: Date;
  };
  categories: string[];
  departments: string[];
  status: ('under' | 'over' | 'on-track')[];
  statuses: ('under' | 'over' | 'on-track')[];
  minVariance?: number;
  maxVariance?: number;
  searchTerm: string;
  amountRange: {
    min: number;
    max: number;
  };
  sortBy: string;
  sortOrder: 'asc' | 'desc';
}

export interface BudgetAlert {
  id: string;
  type: 'warning' | 'error' | 'info' | 'critical' | 'success';
  title: string;
  message: string;
  category?: string;
  variance?: number;
  threshold?: number;
  createdAt: Date;
  amount?: number;
  actionRequired?: boolean;
  timestamp?: Date;
}

export interface ExportOptions {
  format: 'pdf' | 'excel' | 'csv' | 'json';
  includeCharts: boolean;
  dateRange: {
    start: Date;
    end: Date;
  };
  categories?: string[];
  includeSummary?: boolean;
  includeVarianceAnalysis?: boolean;
  includeTrendData?: boolean;
  customFields?: string[];
}

export interface DashboardMetrics {
  budgetUtilization: number;
  averageVariance: number;
  categoriesOverBudget: number;
  totalSavings: number;
  riskScore: number;
  totalCategories: number;
  trendData?: ChartDataPoint[];
}