import { createFileRoute } from '@tanstack/react-router';
import { TrainingBudgetAnalysis } from '~/components/training-budget-analysis';

export const Route = createFileRoute('/training-budget-analysis')(
  {
    component: TrainingBudgetAnalysisPage,
    meta: () => [
      {
        title: 'Training Budget Analysis - Actual vs Plan',
        description: 'Comprehensive analysis of training budget performance with actual vs planned comparisons, variance analysis, and actionable insights.'
      }
    ]
  }
);

function TrainingBudgetAnalysisPage() {
  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">
          Training Budget Analysis
        </h1>
        <p className="text-muted-foreground">
          Monitor and analyze training budget performance with comprehensive actual vs planned comparisons, 
          variance analysis, and actionable insights to optimize your training investments.
        </p>
      </div>
      
      <TrainingBudgetAnalysis />
    </div>
  );
}