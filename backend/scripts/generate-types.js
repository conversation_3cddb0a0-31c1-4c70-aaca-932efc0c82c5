#!/usr/bin/env node

/**
 * Script to generate TypeScript types for the frontend
 * This extracts the AppRouter type and other shared types
 */

import { writeFileSync, mkdirSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const typesContent = `// Auto-generated types from backend
// Do not edit this file directly

// Re-export types from backend
export type VectorDocument = {
  id: string;
  vector: number[];
  metadata?: Record<string, any>;
};

export type Collection = {
  name: string;
  statistics: any;
  loaded: boolean;
  error?: string;
};

export type SearchResult = {
  id: string;
  score: number;
  metadata?: Record<string, any>;
};

export type DatabaseTable = {
  name: string;
  type: string;
  schema: string;
};

export type TableColumn = {
  name: string;
  type: string;
  nullable: boolean;
  default: any;
  maxLength?: number;
  precision?: number;
  scale?: number;
};

export type EtcdKeyValue = {
  key: string;
  value: string;
  createRevision: number;
  modRevision: number;
  version: number;
};

export type ServiceInstance = {
  id: string;
  host: string;
  port: number;
  healthy: boolean;
  lastSeen: string;
  metadata?: Record<string, any>;
};

export type DockerContainer = {
  id: string;
  name: string;
  image: string;
  status: string;
  ports?: string[];
  created: string;
  stats?: any;
};

export type SystemMetrics = {
  cpu: {
    usage: number;
    cores: number;
  };
  memory: {
    total: number;
    used: number;
    percentage: number;
  };
  disk: {
    total: number;
    used: number;
    percentage: number;
  };
  network: {
    bytesIn: number;
    bytesOut: number;
  };
};

export type ServiceHealth = {
  name: string;
  status: 'healthy' | 'warning' | 'error' | 'unknown';
  responseTime?: number;
  lastCheck?: string;
};

export type SystemStats = {
  system: {
    hostname: string;
    platform: string;
    arch: string;
    uptime: string;
  };
  containers: {
    total: number;
    running: number;
  };
  docker: {
    running: boolean;
  };
};

// AI Model types
export type ModelType = 'embedding' | 'llm' | 'classification' | 'generation' | 'other';
export type ModelStatus = 'idle' | 'loading' | 'running' | 'error';
export type TrainingStatus = 'pending' | 'running' | 'completed' | 'failed';

export type AIModel = {
  id: string;
  name: string;
  type: ModelType;
  version: string;
  status: ModelStatus;
  accuracy?: number;
  lastTrained?: Date;
  datasetSize?: number;
  framework: string;
  size: string;
  parameters?: string;
  description?: string;
  dockerImage?: string;
  containerId?: string;
  modelPath?: string;
  config?: any;
  createdAt: Date;
  updatedAt: Date;
};

export type TrainingJob = {
  id: string;
  modelId: string;
  status: TrainingStatus;
  progress: number;
  startTime: Date;
  endTime?: Date;
  epochs: number;
  currentEpoch: number;
  loss?: number;
  accuracy?: number;
  batchSize?: number;
  learningRate?: number;
  datasetPath?: string;
  config?: any;
  logs?: string;
  createdAt: Date;
  updatedAt: Date;
};

// User types
export type UserRole = 'admin' | 'user' | 'viewer';

export type User = {
  id: string;
  email: string;
  firstName?: string;
  lastName?: string;
  username?: string;
  role: UserRole;
  avatar?: string;
  isActive: boolean;
  lastLogin?: Date;
  createdAt: Date;
  updatedAt: Date;
};

// API Response types
export type ApiResponse<T = any> = {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  timestamp: string;
};

export type Alert = {
  id: string;
  type: 'info' | 'warning' | 'error' | 'success';
  title: string;
  message: string;
  timestamp: Date;
  read: boolean;
};

// AppRouter type placeholder - will be properly typed when backend is running
export type AppRouter = any;
`;

// Ensure the types directory exists
const typesDir = join(__dirname, '../../backend-gui/src/types');
mkdirSync(typesDir, { recursive: true });

// Write the types file
const typesFile = join(typesDir, 'api.ts');
writeFileSync(typesFile, typesContent);

console.log('✅ Types generated successfully at:', typesFile);
