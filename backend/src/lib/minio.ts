import { Client } from 'minio';
import { logger } from '../utils/logger.js';

// MinIO client configuration
export const minioClient = new Client({
  endPoint: process.env.MINIO_ENDPOINT || 'localhost',
  port: parseInt(process.env.MINIO_PORT || '9000'),
  useSSL: process.env.MINIO_USE_SSL === 'true',
  accessKey: process.env.MINIO_ACCESS_KEY || 'minioadmin',
  secretKey: process.env.MINIO_SECRET_KEY || 'minioadmin',
});

// Default bucket name
export const DEFAULT_BUCKET = process.env.MINIO_DEFAULT_BUCKET || process.env.MINIO_BUCKET || 'file-storage';

// Initialize MinIO - ensure bucket exists
export async function initializeMinIO() {
  try {
    // Check if bucket exists, create if not
    const bucketExists = await minioClient.bucketExists(DEFAULT_BUCKET);
    if (!bucketExists) {
      await minioClient.makeBucket(DEFAULT_BUCKET);
      logger.info(`Created MinIO bucket: ${DEFAULT_BUCKET}`);
    }
    
    // Set bucket policy for public read access (adjust as needed)
    const policy = {
      Version: '2012-10-17',
      Statement: [
        {
          Effect: 'Allow',
          Principal: { AWS: ['*'] },
          Action: ['s3:GetObject'],
          Resource: [`arn:aws:s3:::${DEFAULT_BUCKET}/*`],
        },
      ],
    };
    
    await minioClient.setBucketPolicy(DEFAULT_BUCKET, JSON.stringify(policy));
    logger.info('MinIO initialized successfully');
  } catch (error) {
    logger.error('Failed to initialize MinIO:', error);
    throw error;
  }
}

// Helper function to get file URL
export function getFileUrl(objectName: string, bucketName: string = DEFAULT_BUCKET): string {
  const endpoint = process.env.MINIO_ENDPOINT || 'localhost';
  const port = process.env.MINIO_PORT || '9000';
  const useSSL = process.env.MINIO_USE_SSL === 'true';
  const protocol = useSSL ? 'https' : 'http';
  const portSuffix = (useSSL && port === '443') || (!useSSL && port === '80') ? '' : `:${port}`;

  return `${protocol}://${endpoint}${portSuffix}/${bucketName}/${objectName}`;
}

// Upload file to MinIO
export async function uploadFile(
  objectName: string,
  buffer: Buffer,
  metadata?: Record<string, string>,
  bucketName: string = DEFAULT_BUCKET
): Promise<{ etag: string; url: string }> {
  try {
    const result = await minioClient.putObject(bucketName, objectName, buffer, buffer.length, metadata);
    const url = getFileUrl(objectName, bucketName);

    logger.info(`File uploaded successfully: ${objectName}`);
    return { etag: result.etag, url };
  } catch (error) {
    logger.error(`Failed to upload file ${objectName}:`, error);
    throw error;
  }
}

// Download file from MinIO
export async function downloadFile(
  objectName: string,
  bucketName: string = DEFAULT_BUCKET
): Promise<Buffer> {
  try {
    const stream = await minioClient.getObject(bucketName, objectName);
    const chunks: Buffer[] = [];

    return new Promise((resolve, reject) => {
      stream.on('data', (chunk) => chunks.push(chunk));
      stream.on('end', () => resolve(Buffer.concat(chunks)));
      stream.on('error', reject);
    });
  } catch (error) {
    logger.error(`Failed to download file ${objectName}:`, error);
    throw error;
  }
}

// Delete file from MinIO
export async function deleteFile(
  objectName: string,
  bucketName: string = DEFAULT_BUCKET
): Promise<void> {
  try {
    await minioClient.removeObject(bucketName, objectName);
    logger.info(`File deleted successfully: ${objectName}`);
  } catch (error) {
    logger.error(`Failed to delete file ${objectName}:`, error);
    throw error;
  }
}

// Get file info
export async function getFileInfo(
  objectName: string,
  bucketName: string = DEFAULT_BUCKET
): Promise<any> {
  try {
    const stat = await minioClient.statObject(bucketName, objectName);
    return {
      ...stat,
      url: getFileUrl(objectName, bucketName),
    };
  } catch (error) {
    logger.error(`Failed to get file info for ${objectName}:`, error);
    throw error;
  }
}

// List files in bucket
export async function listFiles(
  prefix?: string,
  bucketName: string = DEFAULT_BUCKET
): Promise<any[]> {
  try {
    const objects: any[] = [];
    const stream = minioClient.listObjects(bucketName, prefix, true);

    return new Promise((resolve, reject) => {
      stream.on('data', (obj) => {
        objects.push({
          ...obj,
          url: getFileUrl(obj.name, bucketName),
        });
      });
      stream.on('end', () => resolve(objects));
      stream.on('error', reject);
    });
  } catch (error) {
    logger.error('Failed to list files:', error);
    throw error;
  }
}

// Generate presigned URL for temporary access
export async function generatePresignedUrl(
  objectName: string,
  expiry: number = 24 * 60 * 60, // 24 hours in seconds
  bucketName: string = DEFAULT_BUCKET
): Promise<string> {
  try {
    const url = await minioClient.presignedGetObject(bucketName, objectName, expiry);
    logger.info(`Generated presigned URL for ${objectName}`);
    return url;
  } catch (error) {
    logger.error(`Failed to generate presigned URL for ${objectName}:`, error);
    throw error;
  }
}
