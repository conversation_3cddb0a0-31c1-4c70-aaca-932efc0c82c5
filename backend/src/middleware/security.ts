import { FastifyRequest, FastifyReply } from 'fastify';
import { logger } from '../utils/logger.js';
import { redisService } from '../services/cache/redis.js';

// Rate limiting configuration
interface RateLimitConfig {
  windowMs: number; // Time window in milliseconds
  maxRequests: number; // Maximum requests per window
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
  keyGenerator?: (request: FastifyRequest) => string;
}

// Default rate limit configurations
export const rateLimitConfigs = {
  default: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 100, // 100 requests per 15 minutes
  },
  auth: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 5, // 5 login attempts per 15 minutes
  },
  api: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 60, // 60 requests per minute
  },
  upload: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 10, // 10 uploads per minute
  },
};

// Rate limiting middleware
export function createRateLimiter(config: RateLimitConfig) {
  return async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const key = config.keyGenerator 
        ? config.keyGenerator(request)
        : `rate_limit:${request.ip}:${request.routerPath || 'default'}`;

      const current = await redisService.get(key);
      const requests = current ? parseInt(current) : 0;

      if (requests >= config.maxRequests) {
        const ttl = await redisService.ttl(key);
        const resetTime = new Date(Date.now() + (ttl * 1000));

        reply.status(429).send({
          error: 'Too Many Requests',
          message: 'Rate limit exceeded. Please try again later.',
          retryAfter: ttl,
          resetTime: resetTime.toISOString(),
        });
        return;
      }

      // Increment counter
      if (requests === 0) {
        await redisService.setex(key, Math.ceil(config.windowMs / 1000), '1');
      } else {
        await redisService.incr(key);
      }

      // Add rate limit headers
      reply.header('X-RateLimit-Limit', config.maxRequests);
      reply.header('X-RateLimit-Remaining', Math.max(0, config.maxRequests - requests - 1));
      reply.header('X-RateLimit-Reset', new Date(Date.now() + config.windowMs).toISOString());

    } catch (error) {
      logger.error('Rate limiting error:', error);
      // Continue without rate limiting if Redis is down
    }
  };
}

// Security headers middleware
export async function securityHeaders(request: FastifyRequest, reply: FastifyReply) {
  // Security headers
  reply.header('X-Content-Type-Options', 'nosniff');
  reply.header('X-Frame-Options', 'DENY');
  reply.header('X-XSS-Protection', '1; mode=block');
  reply.header('Referrer-Policy', 'strict-origin-when-cross-origin');
  reply.header('Permissions-Policy', 'geolocation=(), microphone=(), camera=()');
  
  // Content Security Policy
  const csp = [
    "default-src 'self'",
    "script-src 'self' 'unsafe-inline' 'unsafe-eval'", // Adjust based on your needs
    "style-src 'self' 'unsafe-inline'",
    "img-src 'self' data: https:",
    "font-src 'self'",
    "connect-src 'self' ws: wss:",
    "media-src 'self'",
    "object-src 'none'",
    "base-uri 'self'",
    "form-action 'self'",
    "frame-ancestors 'none'",
  ].join('; ');
  
  reply.header('Content-Security-Policy', csp);

  // HSTS (only in production with HTTPS)
  if (process.env.NODE_ENV === 'production' && request.protocol === 'https') {
    reply.header('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload');
  }
}

// Input validation and sanitization
export function sanitizeInput(input: any): any {
  if (typeof input === 'string') {
    // Remove potentially dangerous characters
    return input
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') // Remove script tags
      .replace(/javascript:/gi, '') // Remove javascript: protocol
      .replace(/on\w+\s*=/gi, '') // Remove event handlers
      .trim();
  }
  
  if (Array.isArray(input)) {
    return input.map(sanitizeInput);
  }
  
  if (input && typeof input === 'object') {
    const sanitized: any = {};
    for (const [key, value] of Object.entries(input)) {
      sanitized[sanitizeInput(key)] = sanitizeInput(value);
    }
    return sanitized;
  }
  
  return input;
}

// Request size limiting
export function createRequestSizeLimit(maxSize: number) {
  return async (request: FastifyRequest, reply: FastifyReply) => {
    const contentLength = request.headers['content-length'];
    
    if (contentLength && parseInt(contentLength) > maxSize) {
      reply.status(413).send({
        error: 'Payload Too Large',
        message: `Request size exceeds maximum allowed size of ${maxSize} bytes`,
        maxSize,
      });
      return;
    }
  };
}

// IP whitelist/blacklist middleware
export function createIPFilter(options: {
  whitelist?: string[];
  blacklist?: string[];
}) {
  return async (request: FastifyRequest, reply: FastifyReply) => {
    const clientIP = request.ip;
    
    // Check blacklist first
    if (options.blacklist && options.blacklist.includes(clientIP)) {
      reply.status(403).send({
        error: 'Forbidden',
        message: 'Access denied from this IP address',
      });
      return;
    }
    
    // Check whitelist if provided
    if (options.whitelist && !options.whitelist.includes(clientIP)) {
      reply.status(403).send({
        error: 'Forbidden',
        message: 'Access denied. IP not in whitelist',
      });
      return;
    }
  };
}

// Request logging middleware
export async function requestLogger(request: FastifyRequest, reply: FastifyReply) {
  const start = Date.now();

  // Log request
  logger.info({
    method: request.method,
    url: request.url,
    ip: request.ip,
    userAgent: request.headers['user-agent'],
    timestamp: new Date().toISOString(),
  }, 'Incoming request');

  // Log response when finished
  reply.raw.on('finish', () => {
    const duration = Date.now() - start;

    logger.info({
      method: request.method,
      url: request.url,
      statusCode: reply.statusCode,
      duration,
      ip: request.ip,
      timestamp: new Date().toISOString(),
    }, 'Request completed');
  });
}

// API key validation middleware
export function createAPIKeyValidator(validKeys: string[]) {
  return async (request: FastifyRequest, reply: FastifyReply) => {
    const apiKey = request.headers['x-api-key'] as string;
    
    if (!apiKey) {
      reply.status(401).send({
        error: 'Unauthorized',
        message: 'API key is required',
      });
      return;
    }
    
    if (!validKeys.includes(apiKey)) {
      reply.status(401).send({
        error: 'Unauthorized',
        message: 'Invalid API key',
      });
      return;
    }
  };
}

// CORS configuration
export const corsOptions = {
  origin: (origin: string, callback: (err: Error | null, allow?: boolean) => void) => {
    const allowedOrigins = [
      'http://localhost:3000',
      'http://localhost:5173',
      'http://localhost:8080',
      process.env.FRONTEND_URL,
    ].filter(Boolean);

    // Allow requests with no origin (mobile apps, etc.)
    if (!origin) return callback(null, true);
    
    if (allowedOrigins.includes(origin)) {
      callback(null, true);
    } else {
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-API-Key'],
};

// Helmet-like security configuration
export const securityConfig = {
  contentTypeParser: {
    parseAs: 'string',
    bodyLimit: 10485760, // 10MB
  },
  trustProxy: true, // Enable if behind a proxy
};
