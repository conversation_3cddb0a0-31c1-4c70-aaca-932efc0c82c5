import { FastifyRequest, FastifyReply, FastifyError } from 'fastify';
import { logger } from '../utils/logger.js';

export interface ApiError extends Error {
  statusCode?: number;
  code?: string;
  details?: any;
}

export class AppError extends Error implements ApiError {
  public statusCode: number;
  public code: string;
  public details?: any;

  constructor(message: string, statusCode: number = 500, code?: string, details?: any) {
    super(message);
    this.name = 'AppError';
    this.statusCode = statusCode;
    this.code = code || 'INTERNAL_ERROR';
    this.details = details;

    // Maintains proper stack trace for where our error was thrown
    Error.captureStackTrace(this, AppError);
  }
}

// Predefined error types
export class ValidationError extends AppError {
  constructor(message: string, details?: any) {
    super(message, 400, 'VALIDATION_ERROR', details);
    this.name = 'ValidationError';
  }
}

export class NotFoundError extends AppError {
  constructor(resource: string = 'Resource') {
    super(`${resource} not found`, 404, 'NOT_FOUND');
    this.name = 'NotFoundError';
  }
}

export class UnauthorizedError extends AppError {
  constructor(message: string = 'Unauthorized') {
    super(message, 401, 'UNAUTHORIZED');
    this.name = 'UnauthorizedError';
  }
}

export class ForbiddenError extends AppError {
  constructor(message: string = 'Forbidden') {
    super(message, 403, 'FORBIDDEN');
    this.name = 'ForbiddenError';
  }
}

export class ConflictError extends AppError {
  constructor(message: string, details?: any) {
    super(message, 409, 'CONFLICT', details);
    this.name = 'ConflictError';
  }
}

export class ServiceUnavailableError extends AppError {
  constructor(service: string) {
    super(`${service} service is currently unavailable`, 503, 'SERVICE_UNAVAILABLE');
    this.name = 'ServiceUnavailableError';
  }
}

// Enhanced error handler for Fastify
export function createErrorHandler(isDevelopment: boolean = false) {
  return (error: FastifyError, request: FastifyRequest, reply: FastifyReply) => {
    // Log the error
    logger.error({
      error: {
        name: error.name,
        message: error.message,
        stack: error.stack,
        statusCode: error.statusCode,
        code: error.code,
      },
      request: {
        method: request.method,
        url: request.url,
        headers: request.headers,
        params: request.params,
        query: request.query,
      },
    }, 'Request error occurred');

    // Handle validation errors
    if (error.validation) {
      return reply.status(400).send({
        error: 'Validation Error',
        message: 'Invalid request data',
        details: error.validation,
        code: 'VALIDATION_ERROR',
        timestamp: new Date().toISOString(),
      });
    }

    // Handle tRPC errors
    if (error.name === 'TRPCError') {
      const statusCode = getStatusCodeFromTRPCCode(error.code);
      return reply.status(statusCode).send({
        error: error.name,
        message: error.message,
        code: error.code,
        timestamp: new Date().toISOString(),
        ...(isDevelopment && { stack: error.stack }),
      });
    }

    // Handle custom app errors
    if (error instanceof AppError) {
      return reply.status(error.statusCode).send({
        error: error.name,
        message: error.message,
        code: error.code,
        details: error.details,
        timestamp: new Date().toISOString(),
        ...(isDevelopment && { stack: error.stack }),
      });
    }

    // Handle known HTTP errors
    if (error.statusCode) {
      return reply.status(error.statusCode).send({
        error: error.name || 'HTTP Error',
        message: error.message,
        code: error.code || 'HTTP_ERROR',
        timestamp: new Date().toISOString(),
        ...(isDevelopment && { stack: error.stack }),
      });
    }

    // Handle database errors
    if (error.message.includes('database') || error.message.includes('connection')) {
      return reply.status(503).send({
        error: 'Database Error',
        message: 'Database service is temporarily unavailable',
        code: 'DATABASE_ERROR',
        timestamp: new Date().toISOString(),
        ...(isDevelopment && { details: error.message }),
      });
    }

    // Handle rate limiting errors
    if (error.statusCode === 429) {
      return reply.status(429).send({
        error: 'Rate Limit Exceeded',
        message: 'Too many requests, please try again later',
        code: 'RATE_LIMIT_EXCEEDED',
        timestamp: new Date().toISOString(),
      });
    }

    // Default internal server error
    return reply.status(500).send({
      error: 'Internal Server Error',
      message: isDevelopment ? error.message : 'Something went wrong on our end',
      code: 'INTERNAL_ERROR',
      timestamp: new Date().toISOString(),
      ...(isDevelopment && { 
        stack: error.stack,
        details: error 
      }),
    });
  };
}

// Helper function to map tRPC error codes to HTTP status codes
function getStatusCodeFromTRPCCode(code: string): number {
  const codeMap: Record<string, number> = {
    'BAD_REQUEST': 400,
    'UNAUTHORIZED': 401,
    'FORBIDDEN': 403,
    'NOT_FOUND': 404,
    'METHOD_NOT_SUPPORTED': 405,
    'TIMEOUT': 408,
    'CONFLICT': 409,
    'PRECONDITION_FAILED': 412,
    'PAYLOAD_TOO_LARGE': 413,
    'UNPROCESSABLE_CONTENT': 422,
    'TOO_MANY_REQUESTS': 429,
    'CLIENT_CLOSED_REQUEST': 499,
    'INTERNAL_SERVER_ERROR': 500,
  };

  return codeMap[code] || 500;
}

// Async error wrapper for route handlers
export function asyncHandler(fn: Function) {
  return (request: FastifyRequest, reply: FastifyReply) => {
    return Promise.resolve(fn(request, reply)).catch((error) => {
      throw error;
    });
  };
}

// Error response helper
export function createErrorResponse(
  message: string,
  statusCode: number = 500,
  code?: string,
  details?: any
) {
  return {
    error: true,
    message,
    code: code || 'ERROR',
    statusCode,
    details,
    timestamp: new Date().toISOString(),
  };
}

// Success response helper
export function createSuccessResponse<T>(
  data: T,
  message?: string,
  meta?: any
) {
  return {
    success: true,
    data,
    message,
    meta,
    timestamp: new Date().toISOString(),
  };
}
