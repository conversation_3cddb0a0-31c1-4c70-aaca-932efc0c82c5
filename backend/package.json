{"name": "@w-o-w/backend", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "tsx watch src/server.ts", "start": "node dist/server.js", "build": "tsc && tsc-alias", "db:generate": "drizzle-kit generate", "db:migrate": "tsx src/scripts/migrate.ts", "db:studio": "drizzle-kit studio", "db:seed": "tsx src/scripts/seed.ts", "type-check": "tsc --noEmit", "lint": "biome check .", "lint:fix": "biome check --apply .", "format": "biome format --write .", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage", "test:ui": "vitest --ui"}, "dependencies": {"@fastify/cookie": "^10.0.1", "@fastify/cors": "^10.0.1", "@fastify/helmet": "^12.0.1", "@fastify/multipart": "^9.0.1", "@fastify/rate-limit": "^10.1.1", "@lucia-auth/adapter-drizzle": "^1.1.0", "@node-rs/argon2": "^2.0.0", "@tanstack/react-query": "^5.79.0", "@tanstack/react-router": "^1.83.0", "@tanstack/start": "^1.83.0", "@trpc/client": "11.0.0-rc.553", "@trpc/react-query": "11.0.0-rc.553", "@trpc/server": "11.0.0-rc.553", "@types/minio": "^7.1.0", "@xyflow/react": "^12.6.4", "@zilliz/milvus2-sdk-node": "^2.5.10", "bullmq": "^5.35.6", "dockerode": "^4.0.7", "dotenv": "^16.4.7", "drizzle-orm": "^0.36.4", "drizzle-valibot": "^0.2.0", "etcd3": "^1.1.2", "fastify": "^5.1.0", "minio": "^8.0.5", "pino": "^9.5.0", "pino-pretty": "^13.0.0", "postgres": "^3.4.5", "redis": "^4.7.0", "superjson": "^2.2.1", "systeminformation": "^5.27.1", "tsx": "^4.19.4", "valibot": "^1.0.0", "ws": "^8.18.2"}, "devDependencies": {"@biomejs/biome": "^1.9.4", "@types/dockerode": "^3.3.40", "@types/node": "^22.10.2", "@types/pg": "^8.15.2", "@vitest/coverage-v8": "^3.2.3", "@vitest/ui": "^3.1.4", "drizzle-kit": "^0.31.1", "playwright": "^1.52.0", "tsc-alias": "^1.8.10", "typescript": "^5.7.2", "vitest": "^3.1.4"}}