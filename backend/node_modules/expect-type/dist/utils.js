"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * @internal
 */
const secret = Symbol('secret');
/**
 * @internal
 */
const mismatch = Symbol('mismatch');
/**
 * A type which should match anything passed as a value but *doesn't*
 * match {@linkcode Mismatch}. It helps TypeScript select the right overload
 * for {@linkcode PositiveExpectTypeOf.toEqualTypeOf | .toEqualTypeOf()} and
 * {@linkcode PositiveExpectTypeOf.toMatchTypeOf | .toMatchTypeOf()}.
 *
 * @internal
 */
const avalue = Symbol('avalue');
